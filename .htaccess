# Wolf CTF Community - Enhanced Security Configuration
# Apache .htaccess file for additional server-side security

# Disable server signature
ServerTokens Prod
ServerSignature Off

# Security Headers
<IfModule mod_headers.c>
    # Prevent MIME type sniffing
    Header always set X-Content-Type-Options "nosniff"
    
    # Prevent clickjacking
    Header always set X-Frame-Options "DENY"
    
    # Enable XSS protection
    Header always set X-XSS-Protection "1; mode=block"
    
    # Strict Transport Security (HTTPS only)
    Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"
    
    # Content Security Policy
    Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.tailwindcss.com https://www.gstatic.com https://cdnjs.cloudflare.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdnjs.cloudflare.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data:; connect-src 'self' https://*.firebaseapp.com https://*.googleapis.com; frame-ancestors 'none'; base-uri 'self'; form-action 'self'"
    
    # Referrer Policy
    Header always set Referrer-Policy "no-referrer"
    
    # Permissions Policy
    Header always set Permissions-Policy "camera=(), microphone=(), geolocation=(), payment=(), usb=(), magnetometer=(), gyroscope=(), accelerometer=()"
    
    # Remove server information
    Header unset Server
    Header unset X-Powered-By
    
    # Prevent caching of sensitive pages
    <FilesMatch "\.(html|htm|php)$">
        Header set Cache-Control "no-cache, no-store, must-revalidate"
        Header set Pragma "no-cache"
        Header set Expires "0"
    </FilesMatch>
</IfModule>

# Disable directory browsing
Options -Indexes

# Disable access to sensitive files
<FilesMatch "\.(htaccess|htpasswd|ini|log|sh|inc|bak|config|sql)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Protect against common attacks
<IfModule mod_rewrite.c>
    RewriteEngine On
    
    # Block suspicious requests
    RewriteCond %{QUERY_STRING} (<|%3C).*script.*(>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} GLOBALS(=|\[|\%[0-9A-Z]{0,2}) [OR]
    RewriteCond %{QUERY_STRING} _REQUEST(=|\[|\%[0-9A-Z]{0,2}) [OR]
    RewriteCond %{QUERY_STRING} ^.*(\[|\]|\(|\)|<|>|ê|"|;|\?|\*|=$).* [NC,OR]
    RewriteCond %{QUERY_STRING} ^.*("|'|<|>|\|).* [NC,OR]
    RewriteCond %{QUERY_STRING} ^.*(%0A|%0D|%27|%3C|%3E|%00).* [NC,OR]
    RewriteCond %{QUERY_STRING} ^.*(globals|encode|localhost|loopback).* [NC,OR]
    RewriteCond %{QUERY_STRING} ^.*(request|select|insert|union|declare).* [NC]
    RewriteRule ^(.*)$ - [F,L]
    
    # Block bad user agents
    RewriteCond %{HTTP_USER_AGENT} ^$ [OR]
    RewriteCond %{HTTP_USER_AGENT} ^(java|curl|wget) [NC,OR]
    RewriteCond %{HTTP_USER_AGENT} (libwww-perl|python|nikto|scan|java|winhttp|clshttp|loader) [NC,OR]
    RewriteCond %{HTTP_USER_AGENT} (;|<|>|'|"|\)|\(|%0A|%0D|%22|%27|%28|%3C|%3E|%00).*(libwww-perl|python|nikto|scan|java|winhttp|HTTrack|clshttp|archiver|loader|email|harvest|extract|grab|miner) [NC]
    RewriteRule .* - [F,L]
    
    # Block hotlinking
    RewriteCond %{HTTP_REFERER} !^$
    RewriteCond %{HTTP_REFERER} !^https?://(www\.)?yourdomain\.com [NC]
    RewriteRule \.(jpg|jpeg|png|gif|css|js)$ - [F,L]
</IfModule>

# Limit file upload size (if applicable)
LimitRequestBody 10485760

# Disable PHP execution in uploads directory (if applicable)
<Directory "uploads">
    <FilesMatch "\.php$">
        Order Allow,Deny
        Deny from all
    </FilesMatch>
</Directory>

# Force HTTPS redirect
<IfModule mod_rewrite.c>
    RewriteEngine On
    RewriteCond %{HTTPS} off
    RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
</IfModule>

# Compress files for better performance
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Set proper MIME types
<IfModule mod_mime.c>
    AddType application/javascript .js
    AddType text/css .css
</IfModule>

# Prevent access to git files
<FilesMatch "^\.git">
    Order allow,deny
    Deny from all
</FilesMatch>

# Block access to backup files
<FilesMatch "\.(bak|backup|swp|tmp)$">
    Order allow,deny
    Deny from all
</FilesMatch>
