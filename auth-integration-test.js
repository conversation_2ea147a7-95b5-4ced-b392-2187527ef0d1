// Authentication Integration Test Suite
// Comprehensive testing for Firebase authentication with the new connection manager

class AuthIntegrationTest {
    constructor() {
        this.testResults = [];
        this.connectionManager = null;
        this.authSystem = null;
        this.testStartTime = Date.now();
        this.init();
    }

    init() {
        console.log('🧪 Initializing Authentication Integration Tests...');
        
        // Wait for connection manager to be available
        this.waitForDependencies().then(() => {
            this.runIntegrationTests();
        });
    }

    async waitForDependencies(maxWait = 10000) {
        const startTime = Date.now();
        
        while ((Date.now() - startTime) < maxWait) {
            if (window.firebaseConnectionManager && window.authSystem) {
                this.connectionManager = window.firebaseConnectionManager;
                this.authSystem = window.authSystem;
                console.log('✅ Dependencies loaded');
                return true;
            }
            await this.delay(100);
        }
        
        console.warn('⚠️ Some dependencies not loaded, proceeding with available components');
        this.connectionManager = window.firebaseConnectionManager;
        this.authSystem = window.authSystem;
        return false;
    }

    async runIntegrationTests() {
        console.log('🚀 Starting Authentication Integration Tests...');
        
        // Test 1: Connection Manager Integration
        await this.testConnectionManagerIntegration();
        
        // Test 2: Firebase Connection Verification
        await this.testFirebaseConnectionVerification();
        
        // Test 3: Authentication System Integration
        await this.testAuthSystemIntegration();
        
        // Test 4: Form Validation Integration
        await this.testFormValidationIntegration();
        
        // Test 5: Error Handling Integration
        await this.testErrorHandlingIntegration();
        
        // Test 6: Status Indicator Integration
        await this.testStatusIndicatorIntegration();
        
        // Test 7: End-to-End Authentication Flow
        await this.testEndToEndFlow();
        
        // Display comprehensive results
        this.displayIntegrationResults();
    }

    async testConnectionManagerIntegration() {
        console.log('🔗 Testing Connection Manager Integration...');
        
        try {
            // Test connection manager availability
            this.addResult('Connection Manager Available', !!this.connectionManager);
            
            if (this.connectionManager) {
                // Test connection status
                const status = this.connectionManager.getConnectionStatus();
                this.addResult('Connection Status Accessible', !!status);
                this.addResult('Connection Manager Initialized', status.isInitialized);
                
                // Test status callbacks
                let callbackTriggered = false;
                this.connectionManager.onStatusChange(() => {
                    callbackTriggered = true;
                });
                
                // Trigger a status change
                this.connectionManager.updateStatus('test', 'Integration test');
                await this.delay(100);
                
                this.addResult('Status Callbacks Working', callbackTriggered);
            }
            
        } catch (error) {
            this.addResult('Connection Manager Integration Failed', false, error.message);
        }
    }

    async testFirebaseConnectionVerification() {
        console.log('🔥 Testing Firebase Connection Verification...');
        
        try {
            if (this.connectionManager) {
                // Test connection verification
                const connectionResult = await this.connectionManager.verifyFirebaseConnection();
                this.addResult('Firebase Connection Verification', connectionResult);
                
                // Test health check
                await this.connectionManager.performHealthCheck();
                this.addResult('Firebase Health Check', true);
                
                // Test connection status
                const status = this.connectionManager.getConnectionStatus();
                this.addResult('Firebase Connected', status.isConnected);
                
            } else {
                this.addResult('Firebase Connection Verification Skipped', false, 'Connection manager not available');
            }
            
        } catch (error) {
            this.addResult('Firebase Connection Verification Failed', false, error.message);
        }
    }

    async testAuthSystemIntegration() {
        console.log('🔐 Testing Auth System Integration...');
        
        try {
            // Test auth system availability
            this.addResult('Auth System Available', !!this.authSystem);
            
            if (this.authSystem) {
                // Test Firebase ready state
                this.addResult('Auth System Firebase Ready', this.authSystem.isFirebaseReady);
                
                // Test auth object availability
                this.addResult('Auth Object Available', !!this.authSystem.auth);
                
                // Test form elements availability
                const signInForm = document.getElementById('signInForm');
                const signUpForm = document.getElementById('signUpForm');
                
                this.addResult('Sign In Form Available', !!signInForm);
                this.addResult('Sign Up Form Available', !!signUpForm);
                
                // Test event listeners setup
                this.addResult('Event Listeners Setup', 
                    signInForm && signUpForm && 
                    signInForm.onsubmit !== null && 
                    signUpForm.onsubmit !== null
                );
            }
            
        } catch (error) {
            this.addResult('Auth System Integration Failed', false, error.message);
        }
    }

    async testFormValidationIntegration() {
        console.log('📝 Testing Form Validation Integration...');
        
        try {
            // Test email validation
            if (this.authSystem && typeof this.authSystem.isValidEmail === 'function') {
                this.addResult('Email Validation Function Available', true);
                
                // Test email validation logic
                const validEmail = this.authSystem.isValidEmail('<EMAIL>');
                const invalidEmail = this.authSystem.isValidEmail('invalid-email');
                
                this.addResult('Email Validation Logic Working', validEmail && !invalidEmail);
            } else {
                this.addResult('Email Validation Function Available', false);
            }
            
            // Test password validation
            if (this.authSystem && typeof this.authSystem.isStrongPassword === 'function') {
                this.addResult('Password Validation Function Available', true);
                
                // Test password validation logic
                const strongPassword = this.authSystem.isStrongPassword('StrongPass123!');
                const weakPassword = this.authSystem.isStrongPassword('weak');
                
                this.addResult('Password Validation Logic Working', strongPassword && !weakPassword);
            } else {
                this.addResult('Password Validation Function Available', false);
            }
            
            // Test form field highlighting
            if (this.authSystem && typeof this.authSystem.highlightField === 'function') {
                this.addResult('Field Highlighting Function Available', true);
            } else {
                this.addResult('Field Highlighting Function Available', false);
            }
            
        } catch (error) {
            this.addResult('Form Validation Integration Failed', false, error.message);
        }
    }

    async testErrorHandlingIntegration() {
        console.log('🚨 Testing Error Handling Integration...');
        
        try {
            // Test message display system
            if (this.authSystem && typeof this.authSystem.showMessage === 'function') {
                this.addResult('Message Display Function Available', true);
                
                // Test message display
                this.authSystem.showMessage('Test message', 'info');
                await this.delay(100);
                
                const messageContainer = document.getElementById('messageContainer');
                this.addResult('Message Container Working', 
                    messageContainer && !messageContainer.classList.contains('hidden')
                );
                
                // Hide message
                this.authSystem.hideMessage();
            } else {
                this.addResult('Message Display Function Available', false);
            }
            
            // Test error handler integration
            this.addResult('Global Error Handler Available', !!window.errorHandler);
            
            // Test Firebase error handling
            if (this.authSystem && typeof this.authSystem.handleFirebaseError === 'function') {
                this.addResult('Firebase Error Handler Available', true);
            } else {
                this.addResult('Firebase Error Handler Available', false);
            }
            
        } catch (error) {
            this.addResult('Error Handling Integration Failed', false, error.message);
        }
    }

    async testStatusIndicatorIntegration() {
        console.log('📊 Testing Status Indicator Integration...');
        
        try {
            // Test Firebase status indicator
            this.addResult('Firebase Status Indicator Available', !!window.firebaseStatus);
            
            // Test connection manager status indicator
            if (this.connectionManager && this.connectionManager.statusIndicator) {
                this.addResult('Connection Manager Status Indicator Available', true);
                
                // Test status indicator element
                const statusElement = document.getElementById('firebase-connection-status');
                this.addResult('Status Indicator Element Present', !!statusElement);
            } else {
                this.addResult('Connection Manager Status Indicator Available', false);
            }
            
            // Test network diagnostics integration
            this.addResult('Network Diagnostics Available', !!window.networkDiagnostics);
            
        } catch (error) {
            this.addResult('Status Indicator Integration Failed', false, error.message);
        }
    }

    async testEndToEndFlow() {
        console.log('🔄 Testing End-to-End Authentication Flow...');
        
        try {
            // Test complete sign-in flow simulation
            if (this.authSystem && this.connectionManager) {
                // Check if Firebase is ready
                const connectionStatus = this.connectionManager.getConnectionStatus();
                this.addResult('Firebase Ready for Authentication', connectionStatus.isConnected);
                
                // Test form switching
                const signInTab = document.getElementById('signInTab');
                const signUpTab = document.getElementById('signUpTab');
                
                if (signInTab && signUpTab) {
                    // Simulate tab click
                    signUpTab.click();
                    await this.delay(100);
                    
                    const signUpForm = document.getElementById('signUpForm');
                    const signInForm = document.getElementById('signInForm');
                    
                    this.addResult('Tab Switching Working', 
                        signUpForm && !signUpForm.classList.contains('hidden') &&
                        signInForm && signInForm.classList.contains('hidden')
                    );
                    
                    // Switch back
                    signInTab.click();
                    await this.delay(100);
                }
                
                // Test form validation flow
                if (typeof this.authSystem.handleSignIn === 'function') {
                    this.addResult('Sign In Handler Available', true);
                }
                
                if (typeof this.authSystem.handleSignUp === 'function') {
                    this.addResult('Sign Up Handler Available', true);
                }
                
            } else {
                this.addResult('End-to-End Flow Test Skipped', false, 'Required components not available');
            }
            
        } catch (error) {
            this.addResult('End-to-End Flow Test Failed', false, error.message);
        }
    }

    addResult(testName, passed, errorMessage = null) {
        this.testResults.push({
            name: testName,
            passed: passed,
            error: errorMessage,
            timestamp: new Date().toISOString()
        });
        
        const icon = passed ? '✅' : '❌';
        const error = errorMessage ? ` (${errorMessage})` : '';
        console.log(`${icon} ${testName}: ${passed ? 'PASS' : 'FAIL'}${error}`);
    }

    displayIntegrationResults() {
        const totalTime = Date.now() - this.testStartTime;
        const totalTests = this.testResults.length;
        const passedTests = this.testResults.filter(result => result.passed).length;
        const failedTests = totalTests - passedTests;
        const successRate = ((passedTests / totalTests) * 100).toFixed(1);
        
        console.log('\n🧪 Authentication Integration Test Results:');
        console.log('='.repeat(50));
        console.log(`📊 Total Tests: ${totalTests}`);
        console.log(`✅ Passed: ${passedTests}`);
        console.log(`❌ Failed: ${failedTests}`);
        console.log(`📈 Success Rate: ${successRate}%`);
        console.log(`⏱️ Test Duration: ${totalTime}ms`);
        console.log('='.repeat(50));
        
        if (failedTests > 0) {
            console.log('\n❌ Failed Tests:');
            this.testResults
                .filter(result => !result.passed)
                .forEach(result => {
                    console.log(`  • ${result.name}${result.error ? ` - ${result.error}` : ''}`);
                });
        }
        
        // Create visual test results display
        this.createIntegrationTestDisplay(passedTests, totalTests, totalTime, successRate);
        
        // Provide integration recommendations
        this.provideIntegrationRecommendations();
    }

    createIntegrationTestDisplay(passed, total, duration, successRate) {
        // Only show in development
        if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
            const testDisplay = document.createElement('div');
            testDisplay.id = 'auth-integration-test-results';
            testDisplay.style.cssText = `
                position: fixed;
                bottom: 10px;
                left: 10px;
                background: #1a1a1a;
                color: #00aaff;
                padding: 20px;
                font-family: 'Roboto Mono', monospace;
                font-size: 12px;
                border: 3px solid #00aaff;
                border-radius: 8px;
                box-shadow: 0 0 15px rgba(0,170,255,0.3);
                z-index: 9998;
                max-width: 350px;
                cursor: pointer;
            `;
            
            const statusIcon = passed === total ? '🧪✅' : passed > total * 0.8 ? '🧪⚠️' : '🧪❌';
            const statusColor = passed === total ? '#00aa00' : passed > total * 0.8 ? '#ffaa00' : '#ff4444';
            
            testDisplay.innerHTML = `
                <div style="font-weight: bold; margin-bottom: 10px; color: ${statusColor};">
                    ${statusIcon} AUTH INTEGRATION TESTS
                </div>
                <div style="margin-bottom: 5px;">✅ ${passed}/${total} passed (${successRate}%)</div>
                <div style="margin-bottom: 5px;">⏱️ ${duration}ms</div>
                <div style="margin-bottom: 10px;">🔗 Integration Status: ${passed === total ? 'EXCELLENT' : passed > total * 0.8 ? 'GOOD' : 'NEEDS ATTENTION'}</div>
                <div style="font-size: 10px; opacity: 0.7;">Click to hide • Auto-hide in 20s</div>
            `;
            
            testDisplay.addEventListener('click', () => {
                testDisplay.remove();
            });
            
            document.body.appendChild(testDisplay);
            
            // Auto-hide after 20 seconds
            setTimeout(() => {
                if (document.body.contains(testDisplay)) {
                    testDisplay.remove();
                }
            }, 20000);
        }
    }

    provideIntegrationRecommendations() {
        const failedTests = this.testResults.filter(result => !result.passed);
        
        if (failedTests.length === 0) {
            console.log('🎉 All integration tests passed! Your authentication system is fully integrated and working correctly.');
            return;
        }
        
        console.log('\n💡 Integration Recommendations:');
        console.log('-'.repeat(40));
        
        const recommendations = new Set();
        
        failedTests.forEach(test => {
            if (test.name.includes('Connection Manager')) {
                recommendations.add('• Ensure firebase-connection-manager.js is loaded before auth.js');
            }
            if (test.name.includes('Firebase Connection')) {
                recommendations.add('• Check Firebase configuration and network connectivity');
            }
            if (test.name.includes('Auth System')) {
                recommendations.add('• Verify auth.js is properly initialized');
            }
            if (test.name.includes('Form')) {
                recommendations.add('• Check HTML form elements and IDs are correct');
            }
            if (test.name.includes('Validation')) {
                recommendations.add('• Ensure validation functions are properly defined in auth.js');
            }
            if (test.name.includes('Error Handling')) {
                recommendations.add('• Verify error-handler.js is loaded and working');
            }
            if (test.name.includes('Status Indicator')) {
                recommendations.add('• Check status indicator scripts are loaded and initialized');
            }
        });
        
        recommendations.forEach(rec => console.log(rec));
        
        if (recommendations.size === 0) {
            console.log('• Review failed tests above for specific issues');
        }
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // Manual test functions
    async testManualSignIn(email = '<EMAIL>', password = 'Test123!') {
        console.log('🔄 Testing manual sign-in integration...');
        
        if (!this.authSystem) {
            console.log('❌ Auth system not available');
            return false;
        }
        
        try {
            // Fill form fields
            const emailField = document.getElementById('signInEmail');
            const passwordField = document.getElementById('signInPassword');
            
            if (emailField && passwordField) {
                emailField.value = email;
                passwordField.value = password;
                
                // Trigger sign-in
                await this.authSystem.handleSignIn();
                console.log('✅ Manual sign-in test completed');
                return true;
            } else {
                console.log('❌ Form fields not found');
                return false;
            }
        } catch (error) {
            console.log('❌ Manual sign-in test failed:', error.message);
            return false;
        }
    }

    async testManualSignUp(name = 'Test User', email = '<EMAIL>', password = 'Test123!') {
        console.log('🔄 Testing manual sign-up integration...');
        
        if (!this.authSystem) {
            console.log('❌ Auth system not available');
            return false;
        }
        
        try {
            // Switch to sign-up tab
            const signUpTab = document.getElementById('signUpTab');
            if (signUpTab) signUpTab.click();
            
            await this.delay(100);
            
            // Fill form fields
            const nameField = document.getElementById('signUpName');
            const emailField = document.getElementById('signUpEmail');
            const passwordField = document.getElementById('signUpPassword');
            const confirmPasswordField = document.getElementById('signUpConfirmPassword');
            
            if (nameField && emailField && passwordField && confirmPasswordField) {
                nameField.value = name;
                emailField.value = email;
                passwordField.value = password;
                confirmPasswordField.value = password;
                
                // Trigger sign-up
                await this.authSystem.handleSignUp();
                console.log('✅ Manual sign-up test completed');
                return true;
            } else {
                console.log('❌ Form fields not found');
                return false;
            }
        } catch (error) {
            console.log('❌ Manual sign-up test failed:', error.message);
            return false;
        }
    }
}

// Initialize Auth Integration Test
document.addEventListener('DOMContentLoaded', () => {
    // Wait for other systems to load
    setTimeout(() => {
        window.authIntegrationTest = new AuthIntegrationTest();
    }, 2000);
});

// Make test functions available globally
window.testAuthIntegration = () => {
    if (window.authIntegrationTest) {
        return window.authIntegrationTest.runIntegrationTests();
    }
};

window.testManualSignIn = (email, password) => {
    if (window.authIntegrationTest) {
        return window.authIntegrationTest.testManualSignIn(email, password);
    }
};

window.testManualSignUp = (name, email, password) => {
    if (window.authIntegrationTest) {
        return window.authIntegrationTest.testManualSignUp(name, email, password);
    }
};

console.log('🧪 Auth Integration Test loaded. Use testAuthIntegration(), testManualSignIn(), or testManualSignUp() for testing.');