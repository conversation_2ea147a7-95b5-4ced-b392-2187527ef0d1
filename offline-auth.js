// Offline Authentication System for Wolf CTF Community
// Provides fallback authentication when internet is unstable

class OfflineAuthSystem {
    constructor() {
        this.isOnline = navigator.onLine;
        this.connectionRetries = 0;
        this.maxRetries = 5;
        this.retryDelay = 2000; // 2 seconds
        this.offlineUsers = this.loadOfflineUsers();
        this.init();
    }

    init() {
        this.setupNetworkMonitoring();
        this.setupOfflineStorage();
        this.createConnectionStatus();
    }

    // Monitor network status
    setupNetworkMonitoring() {
        window.addEventListener('online', () => {
            this.isOnline = true;
            this.updateConnectionStatus('online', 'Back Online - Syncing...');
            this.syncOfflineData();
        });

        window.addEventListener('offline', () => {
            this.isOnline = false;
            this.updateConnectionStatus('offline', 'Offline Mode - Limited Features');
        });

        // Check connection quality
        this.checkConnectionQuality();
        setInterval(() => this.checkConnectionQuality(), 30000); // Check every 30 seconds
    }

    // Check internet connection quality
    async checkConnectionQuality() {
        if (!this.isOnline) return;

        try {
            const startTime = Date.now();
            // Use safe fetch to avoid recursion
            const response = await window.safeFetch('https://www.google.com/favicon.ico', {
                method: 'HEAD',
                mode: 'no-cors',
                cache: 'no-cache'
            });
            const endTime = Date.now();
            const latency = endTime - startTime;

            if (latency > 5000) {
                this.updateConnectionStatus('slow', 'Slow Connection - May take longer');
            } else if (latency > 2000) {
                this.updateConnectionStatus('moderate', 'Moderate Connection');
            } else {
                this.updateConnectionStatus('good', 'Good Connection');
            }
        } catch (error) {
            this.updateConnectionStatus('poor', 'Poor Connection - Retrying...');
            this.attemptReconnection();
        }
    }

    // Attempt to reconnect
    async attemptReconnection() {
        if (this.connectionRetries >= this.maxRetries) {
            this.updateConnectionStatus('failed', 'Connection Failed - Using Offline Mode');
            return;
        }

        this.connectionRetries++;
        this.updateConnectionStatus('reconnecting', `Reconnecting... (${this.connectionRetries}/${this.maxRetries})`);

        await new Promise(resolve => setTimeout(resolve, this.retryDelay));

        try {
            // Use safe fetch to avoid recursion
            await window.safeFetch('https://www.google.com/favicon.ico', {
                method: 'HEAD',
                mode: 'no-cors',
                cache: 'no-cache'
            });

            this.connectionRetries = 0;
            this.updateConnectionStatus('reconnected', 'Reconnected Successfully!');

            // Try to reinitialize Firebase
            if (window.firebaseAuth) {
                this.updateConnectionStatus('firebase-ready', 'Firebase Ready');
            }
        } catch (error) {
            setTimeout(() => this.attemptReconnection(), this.retryDelay * this.connectionRetries);
        }
    }

    // Setup offline storage
    setupOfflineStorage() {
        // Create offline user storage if it doesn't exist
        if (!localStorage.getItem('offlineUsers')) {
            localStorage.setItem('offlineUsers', JSON.stringify({}));
        }

        // Create pending actions storage
        if (!localStorage.getItem('pendingActions')) {
            localStorage.setItem('pendingActions', JSON.stringify([]));
        }
    }

    // Load offline users
    loadOfflineUsers() {
        try {
            return JSON.parse(localStorage.getItem('offlineUsers') || '{}');
        } catch (error) {
            console.error('Error loading offline users:', error);
            return {};
        }
    }

    // Save offline users
    saveOfflineUsers() {
        try {
            localStorage.setItem('offlineUsers', JSON.stringify(this.offlineUsers));
        } catch (error) {
            console.error('Error saving offline users:', error);
        }
    }

    // Create offline account
    createOfflineAccount(name, email, password) {
        const userId = 'offline_' + Date.now();
        const hashedPassword = this.simpleHash(password);
        
        this.offlineUsers[email] = {
            id: userId,
            name: name,
            email: email,
            password: hashedPassword,
            createdAt: new Date().toISOString(),
            isOffline: true
        };
        
        this.saveOfflineUsers();
        
        // Add to pending actions for sync when online
        this.addPendingAction('createAccount', {
            name, email, password, userId
        });
        
        return {
            user: {
                uid: userId,
                email: email,
                displayName: name
            }
        };
    }

    // Authenticate offline user
    authenticateOfflineUser(email, password) {
        const user = this.offlineUsers[email];
        if (!user) {
            throw new Error('No account found with this email');
        }

        const hashedPassword = this.simpleHash(password);
        if (user.password !== hashedPassword) {
            throw new Error('Incorrect password');
        }

        return {
            user: {
                uid: user.id,
                email: user.email,
                displayName: user.name
            }
        };
    }

    // Simple hash function (not cryptographically secure, just for demo)
    simpleHash(str) {
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32-bit integer
        }
        return hash.toString();
    }

    // Add pending action for sync
    addPendingAction(type, data) {
        try {
            const pending = JSON.parse(localStorage.getItem('pendingActions') || '[]');
            pending.push({
                type,
                data,
                timestamp: new Date().toISOString()
            });
            localStorage.setItem('pendingActions', JSON.stringify(pending));
        } catch (error) {
            console.error('Error adding pending action:', error);
        }
    }

    // Sync offline data when back online
    async syncOfflineData() {
        if (!this.isOnline || !window.firebaseAuth) return;

        try {
            const pending = JSON.parse(localStorage.getItem('pendingActions') || '[]');
            
            for (const action of pending) {
                try {
                    await this.processPendingAction(action);
                } catch (error) {
                    console.error('Error processing pending action:', error);
                }
            }
            
            // Clear processed actions
            localStorage.setItem('pendingActions', JSON.stringify([]));
            this.updateConnectionStatus('synced', 'Data Synced Successfully');
        } catch (error) {
            console.error('Error syncing offline data:', error);
        }
    }

    // Process pending action
    async processPendingAction(action) {
        const { createUserWithEmailAndPassword, updateProfile } = await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js');
        
        switch (action.type) {
            case 'createAccount':
                const { name, email, password } = action.data;
                const userCredential = await createUserWithEmailAndPassword(window.firebaseAuth, email, password);
                await updateProfile(userCredential.user, { displayName: name });
                break;
            // Add more action types as needed
        }
    }

    // Create connection status indicator
    createConnectionStatus() {
        const statusDiv = document.createElement('div');
        statusDiv.id = 'connection-status';
        statusDiv.style.cssText = `
            position: fixed;
            bottom: 10px;
            left: 10px;
            background: #333;
            color: white;
            padding: 8px 12px;
            font-family: 'Roboto Mono', monospace;
            font-size: 11px;
            border: 2px solid #666;
            border-radius: 4px;
            z-index: 9998;
            transition: all 0.3s ease;
            cursor: pointer;
        `;
        
        document.body.appendChild(statusDiv);
        this.statusDiv = statusDiv;
        
        // Initial status
        this.updateConnectionStatus(this.isOnline ? 'checking' : 'offline', 
                                   this.isOnline ? 'Checking Connection...' : 'Offline Mode');
        
        // Click to show connection help
        statusDiv.addEventListener('click', () => {
            this.showConnectionHelp();
        });
    }

    // Update connection status
    updateConnectionStatus(type, message) {
        if (!this.statusDiv) return;

        let backgroundColor, borderColor, icon;
        
        switch (type) {
            case 'online':
            case 'good':
            case 'reconnected':
            case 'synced':
                backgroundColor = '#00aa00';
                borderColor = '#008800';
                icon = '🟢';
                break;
            case 'moderate':
                backgroundColor = '#ffaa00';
                borderColor = '#cc8800';
                icon = '🟡';
                break;
            case 'slow':
            case 'poor':
                backgroundColor = '#ff6600';
                borderColor = '#cc4400';
                icon = '🟠';
                break;
            case 'offline':
            case 'failed':
                backgroundColor = '#666';
                borderColor = '#444';
                icon = '🔴';
                break;
            case 'reconnecting':
            case 'checking':
                backgroundColor = '#0066ff';
                borderColor = '#0044cc';
                icon = '🔄';
                break;
            case 'firebase-ready':
                backgroundColor = '#ff6600';
                borderColor = '#cc4400';
                icon = '🔥';
                break;
        }

        this.statusDiv.style.backgroundColor = backgroundColor;
        this.statusDiv.style.borderColor = borderColor;
        this.statusDiv.innerHTML = `${icon} ${message}`;
        
        // Auto-hide good connections after 5 seconds
        if (type === 'good' || type === 'synced' || type === 'reconnected') {
            setTimeout(() => {
                if (this.statusDiv) {
                    this.statusDiv.style.opacity = '0.5';
                }
            }, 5000);
        } else {
            this.statusDiv.style.opacity = '1';
        }
    }

    // Show connection help
    showConnectionHelp() {
        const helpModal = document.createElement('div');
        helpModal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10001;
            font-family: 'Roboto Mono', monospace;
        `;

        helpModal.innerHTML = `
            <div style="background: white; padding: 30px; border: 4px solid #000; box-shadow: 8px 8px 0 #000; max-width: 500px; text-align: center;">
                <h2 style="font-size: 24px; margin-bottom: 20px; color: #000;">🌐 Connection Status</h2>
                <div style="text-align: left; margin-bottom: 20px; color: #333;">
                    <p><strong>Current Status:</strong> ${this.statusDiv.textContent}</p>
                    <p><strong>Online:</strong> ${this.isOnline ? 'Yes' : 'No'}</p>
                    <p><strong>Retry Attempts:</strong> ${this.connectionRetries}/${this.maxRetries}</p>
                    <hr style="margin: 15px 0;">
                    <p><strong>Offline Features Available:</strong></p>
                    <ul style="margin: 10px 0; padding-left: 20px;">
                        <li>Create temporary accounts</li>
                        <li>Browse component library</li>
                        <li>View cached content</li>
                        <li>Auto-sync when reconnected</li>
                    </ul>
                </div>
                <div style="display: flex; gap: 10px; justify-content: center;">
                    <button id="retryConnection" style="padding: 10px 20px; background: #00aa00; color: white; border: 2px solid #000; font-weight: bold; cursor: pointer;">
                        🔄 RETRY
                    </button>
                    <button id="closeConnectionHelp" style="padding: 10px 20px; background: #666; color: white; border: 2px solid #000; font-weight: bold; cursor: pointer;">
                        CLOSE
                    </button>
                </div>
            </div>
        `;

        document.body.appendChild(helpModal);

        // Handle buttons
        document.getElementById('retryConnection').addEventListener('click', () => {
            document.body.removeChild(helpModal);
            this.connectionRetries = 0;
            this.attemptReconnection();
        });

        document.getElementById('closeConnectionHelp').addEventListener('click', () => {
            document.body.removeChild(helpModal);
        });

        // Close on background click
        helpModal.addEventListener('click', (e) => {
            if (e.target === helpModal) {
                document.body.removeChild(helpModal);
            }
        });
    }

    // Check if offline mode is available
    isOfflineModeAvailable() {
        return !this.isOnline || this.connectionRetries >= this.maxRetries;
    }

    // Get offline user count
    getOfflineUserCount() {
        return Object.keys(this.offlineUsers).length;
    }
}

// Initialize offline auth system
document.addEventListener('DOMContentLoaded', () => {
    window.offlineAuth = new OfflineAuthSystem();
});

// Export for use in other modules
window.OfflineAuthSystem = OfflineAuthSystem;
