// Firebase Connection Test Suite
// Comprehensive testing for Firebase authentication integration

class FirebaseConnectionTest {
    constructor() {
        this.testResults = [];
        this.testStartTime = Date.now();
        this.init();
    }

    init() {
        console.log('🔥 Starting Firebase Connection Tests...');
        this.runTests();
    }

    async runTests() {
        // Test 1: Firebase SDK Loading
        await this.testFirebaseSDKLoading();
        
        // Test 2: Firebase Configuration
        await this.testFirebaseConfiguration();
        
        // Test 3: Authentication Service
        await this.testAuthenticationService();
        
        // Test 4: Connection Events
        await this.testConnectionEvents();
        
        // Test 5: Error Handling
        await this.testErrorHandling();
        
        // Display results
        this.displayResults();
    }

    async testFirebaseSDKLoading() {
        console.log('📦 Testing Firebase SDK loading...');
        
        // Check if Firebase modules are available
        try {
            const firebaseApp = await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js');
            const firebaseAuth = await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js');
            
            this.addResult('Firebase App SDK loaded', !!firebaseApp.initializeApp);
            this.addResult('Firebase Auth SDK loaded', !!firebaseAuth.getAuth);
            this.addResult('Firebase Auth methods available', 
                !!(firebaseAuth.signInWithEmailAndPassword && firebaseAuth.createUserWithEmailAndPassword)
            );
        } catch (error) {
            this.addResult('Firebase SDK loading failed', false, error.message);
        }
    }

    async testFirebaseConfiguration() {
        console.log('⚙️ Testing Firebase configuration...');
        
        // Wait for Firebase to initialize
        await this.waitForFirebase();
        
        this.addResult('Firebase app instance exists', !!window.firebaseApp);
        this.addResult('Firebase auth instance exists', !!window.firebaseAuth);
        
        if (window.firebaseApp) {
            const config = window.firebaseApp.options;
            this.addResult('API key configured', !!config.apiKey);
            this.addResult('Auth domain configured', !!config.authDomain);
            this.addResult('Project ID configured', !!config.projectId);
            this.addResult('Correct project ID', config.projectId === 'cyber-wolf-community-ctf');
        }
    }

    async testAuthenticationService() {
        console.log('🔐 Testing authentication service...');
        
        if (!window.firebaseAuth) {
            this.addResult('Auth service unavailable', false, 'Firebase auth not initialized');
            return;
        }

        try {
            // Test auth state listener
            const auth = window.firebaseAuth;
            this.addResult('Auth instance has required methods', 
                !!(auth.onAuthStateChanged && auth.signOut)
            );
            
            // Test current user state
            this.addResult('Auth current user accessible', 
                auth.currentUser !== undefined
            );
            
            // Test auth state change listener
            let listenerWorking = false;
            const unsubscribe = auth.onAuthStateChanged(() => {
                listenerWorking = true;
            });
            
            setTimeout(() => {
                this.addResult('Auth state listener working', listenerWorking);
                unsubscribe();
            }, 100);
            
        } catch (error) {
            this.addResult('Auth service test failed', false, error.message);
        }
    }

    async testConnectionEvents() {
        console.log('📡 Testing connection events...');
        
        let firebaseReadyEventFired = false;
        let firebaseErrorEventFired = false;
        
        // Listen for Firebase events
        const readyListener = () => {
            firebaseReadyEventFired = true;
        };
        
        const errorListener = () => {
            firebaseErrorEventFired = true;
        };
        
        window.addEventListener('firebaseReady', readyListener);
        window.addEventListener('firebaseError', errorListener);
        
        // Wait a bit to see if events fire
        await this.delay(500);
        
        this.addResult('Firebase ready event system working', 
            typeof window.CustomEvent !== 'undefined'
        );
        
        // Clean up listeners
        window.removeEventListener('firebaseReady', readyListener);
        window.removeEventListener('firebaseError', errorListener);
    }

    async testErrorHandling() {
        console.log('🚨 Testing error handling...');
        
        // Test if error handler exists
        this.addResult('Error handler available', !!window.errorHandler);
        
        // Test if Firebase status indicator exists
        this.addResult('Firebase status indicator available', !!window.firebaseStatus);
        
        // Test network status detection
        this.addResult('Network status detection available', 
            'onLine' in navigator
        );
        
        this.addResult('Currently online', navigator.onLine);
    }

    async waitForFirebase(maxWait = 5000) {
        const startTime = Date.now();
        
        while (!window.firebaseAuth && (Date.now() - startTime) < maxWait) {
            await this.delay(100);
        }
        
        return !!window.firebaseAuth;
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    addResult(testName, passed, errorMessage = null) {
        this.testResults.push({
            name: testName,
            passed: passed,
            error: errorMessage,
            timestamp: new Date().toISOString()
        });
        
        const icon = passed ? '✅' : '❌';
        const error = errorMessage ? ` (${errorMessage})` : '';
        console.log(`${icon} ${testName}: ${passed ? 'PASS' : 'FAIL'}${error}`);
    }

    displayResults() {
        const totalTime = Date.now() - this.testStartTime;
        const totalTests = this.testResults.length;
        const passedTests = this.testResults.filter(result => result.passed).length;
        const failedTests = totalTests - passedTests;
        
        console.log('\n🔥 Firebase Connection Test Results:');
        console.log(`Total Tests: ${totalTests}`);
        console.log(`✅ Passed: ${passedTests}`);
        console.log(`❌ Failed: ${failedTests}`);
        console.log(`Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
        console.log(`Test Duration: ${totalTime}ms`);
        
        if (failedTests > 0) {
            console.log('\n❌ Failed Tests:');
            this.testResults
                .filter(result => !result.passed)
                .forEach(result => {
                    console.log(`  - ${result.name}${result.error ? ` (${result.error})` : ''}`);
                });
        }
        
        // Create visual test results
        this.createTestResultsDisplay(passedTests, totalTests, totalTime);
        
        // Provide recommendations
        this.provideRecommendations();
    }

    createTestResultsDisplay(passed, total, duration) {
        // Only show in development
        if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
            const testDisplay = document.createElement('div');
            testDisplay.id = 'firebase-test-results';
            testDisplay.style.cssText = `
                position: fixed;
                bottom: 10px;
                right: 10px;
                background: #000;
                color: #ff6600;
                padding: 15px;
                font-family: 'Roboto Mono', monospace;
                font-size: 12px;
                border: 2px solid #ff6600;
                box-shadow: 0 0 10px rgba(255,102,0,0.3);
                z-index: 9999;
                max-width: 300px;
                cursor: pointer;
            `;
            
            const successRate = ((passed / total) * 100).toFixed(1);
            const statusIcon = passed === total ? '🔥✅' : passed > total * 0.8 ? '🔥⚠️' : '🔥❌';
            
            testDisplay.innerHTML = `
                <div style="font-weight: bold; margin-bottom: 5px;">${statusIcon} FIREBASE TESTS</div>
                <div>✅ ${passed}/${total} passed (${successRate}%)</div>
                <div>⏱️ ${duration}ms</div>
                <div style="font-size: 10px; margin-top: 5px; opacity: 0.7;">Click to hide</div>
            `;
            
            testDisplay.addEventListener('click', () => {
                testDisplay.remove();
            });
            
            document.body.appendChild(testDisplay);
            
            // Auto-hide after 15 seconds
            setTimeout(() => {
                if (document.body.contains(testDisplay)) {
                    testDisplay.remove();
                }
            }, 15000);
        }
    }

    provideRecommendations() {
        const failedTests = this.testResults.filter(result => !result.passed);
        
        if (failedTests.length === 0) {
            console.log('🎉 All Firebase tests passed! Your connection is working perfectly.');
            return;
        }
        
        console.log('\n💡 Recommendations:');
        
        failedTests.forEach(test => {
            switch (test.name) {
                case 'Firebase App SDK loaded':
                case 'Firebase Auth SDK loaded':
                    console.log('  - Check internet connection and Firebase CDN access');
                    break;
                case 'Firebase app instance exists':
                case 'Firebase auth instance exists':
                    console.log('  - Verify Firebase initialization script is running');
                    break;
                case 'API key configured':
                    console.log('  - Check Firebase configuration in HTML file');
                    break;
                case 'Currently online':
                    console.log('  - Check network connection');
                    break;
                default:
                    console.log(`  - Investigate: ${test.name}`);
            }
        });
    }

    // Manual test functions
    async testSignIn(email = '<EMAIL>', password = 'Test123!') {
        console.log('🔄 Testing sign-in functionality...');
        
        if (!window.firebaseAuth) {
            console.log('❌ Firebase auth not available');
            return false;
        }

        try {
            const { signInWithEmailAndPassword } = await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js');
            const userCredential = await signInWithEmailAndPassword(window.firebaseAuth, email, password);
            console.log('✅ Sign-in test successful:', userCredential.user.email);
            return true;
        } catch (error) {
            console.log('❌ Sign-in test failed:', error.message);
            return false;
        }
    }

    async testSignUp(email = '<EMAIL>', password = 'Test123!') {
        console.log('🔄 Testing sign-up functionality...');
        
        if (!window.firebaseAuth) {
            console.log('❌ Firebase auth not available');
            return false;
        }

        try {
            const { createUserWithEmailAndPassword } = await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js');
            const userCredential = await createUserWithEmailAndPassword(window.firebaseAuth, email, password);
            console.log('✅ Sign-up test successful:', userCredential.user.email);
            return true;
        } catch (error) {
            console.log('❌ Sign-up test failed:', error.message);
            return false;
        }
    }
}

// Initialize Firebase connection test
document.addEventListener('DOMContentLoaded', () => {
    // Wait a bit for Firebase to initialize
    setTimeout(() => {
        window.firebaseConnectionTest = new FirebaseConnectionTest();
    }, 1000);
});

// Make test functions available globally
window.testFirebaseSignIn = (email, password) => {
    if (window.firebaseConnectionTest) {
        return window.firebaseConnectionTest.testSignIn(email, password);
    }
};

window.testFirebaseSignUp = (email, password) => {
    if (window.firebaseConnectionTest) {
        return window.firebaseConnectionTest.testSignUp(email, password);
    }
};

console.log('🔥 Firebase Connection Test loaded. Use testFirebaseSignIn() or testFirebaseSignUp() for manual testing.');
