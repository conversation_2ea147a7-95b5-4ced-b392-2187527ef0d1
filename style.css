body {
  font-family: 'Roboto Mono', monospace;
  background-color: #f5f5f5;
  color: #1a1a1a;
}
.neo-brutalist {
  border: 4px solid #000;
  box-shadow: 8px 8px 0 #000;
  transition: all 0.2s ease;
}
.neo-brutalist:hover {
  box-shadow: 6px 6px 0 #000;
  transform: translate(2px, 2px);
}
h1, h2, h3 {
  font-family: 'Bebas Neue', sans-serif;
  letter-spacing: 2px;
}
.component-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}
.component-preview {
  flex-grow: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1.5rem;
  min-height: 200px;
}

/* Authentication specific styles */
.auth-input:focus {
  outline: none;
  border-color: #4a5568;
  box-shadow: 0 0 0 3px rgba(74, 85, 104, 0.1);
}

.auth-button {
  transition: all 0.2s ease;
  transform: translate(0, 0);
}

.auth-button:hover {
  transform: translate(2px, 2px);
}

.auth-button:active {
  transform: translate(0, 0);
}

/* Loading spinner animation */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.fa-spin {
  animation: spin 1s linear infinite;
}

/* Message animations */
.message-enter {
  animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Enhanced authentication animations */
@keyframes shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
  20%, 40%, 60%, 80% { transform: translateX(5px); }
}

@keyframes successPulse {
  0% { opacity: 0; transform: scale(0.8); }
  50% { opacity: 1; transform: scale(1.1); }
  100% { opacity: 0; transform: scale(1); }
}

@keyframes successBounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-20px); }
  60% { transform: translateY(-10px); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

/* Form field focus enhancements */
.auth-input {
  transition: all 0.3s ease;
}

.auth-input:focus {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Button hover enhancements */
.auth-button {
  position: relative;
  overflow: hidden;
}

.auth-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.auth-button:hover::before {
  left: 100%;
}

/* Success message enhancements */
.message-success {
  animation: messageSlideIn 0.5s ease-out, messagePulse 2s ease-in-out infinite;
}

@keyframes messageSlideIn {
  from {
    transform: translateX(100%) scale(0.8);
    opacity: 0;
  }
  to {
    transform: translateX(0) scale(1);
    opacity: 1;
  }
}

@keyframes messagePulse {
  0%, 100% { box-shadow: 4px 4px 0 #000; }
  50% { box-shadow: 6px 6px 0 #000, 0 0 20px rgba(0, 255, 0, 0.3); }
}

/* Password strength indicator styles */
#passwordStrength {
  animation: fadeInUp 0.3s ease-out;
}

#passwordStrength .bg-red-500 {
  background: linear-gradient(90deg, #ef4444, #dc2626);
}

#passwordStrength .bg-red-400 {
  background: linear-gradient(90deg, #f87171, #ef4444);
}

#passwordStrength .bg-yellow-400 {
  background: linear-gradient(90deg, #facc15, #eab308);
}

#passwordStrength .bg-blue-400 {
  background: linear-gradient(90deg, #60a5fa, #3b82f6);
}

#passwordStrength .bg-green-400 {
  background: linear-gradient(90deg, #4ade80, #22c55e);
}

#passwordStrength .bg-green-500 {
  background: linear-gradient(90deg, #22c55e, #16a34a);
}

/* Password strength progress bar animation */
#passwordStrength .h-3 > div {
  transition: width 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

#passwordStrength .h-3 > div::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 25%, rgba(255,255,255,0.2) 25%, rgba(255,255,255,0.2) 50%, transparent 50%, transparent 75%, rgba(255,255,255,0.2) 75%);
  background-size: 8px 8px;
  animation: progressStripes 1s linear infinite;
}

@keyframes progressStripes {
  0% { background-position: 0 0; }
  100% { background-position: 8px 0; }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Password requirement checkmarks */
#passwordStrength .text-green-600 {
  font-weight: bold;
}

#passwordStrength .text-red-600 {
  font-weight: bold;
}

/* Field error styles */
.field-error {
  animation: fadeInUp 0.3s ease-out;
}

/* Touch device optimizations */
.touch-manipulation {
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Input field touch optimizations */
input.touch-manipulation {
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
  font-size: 16px; /* Prevents zoom on iOS */
}

/* Button active states for touch */
button:active {
  transform: translate(2px, 2px);
  box-shadow: 2px 2px 0 #000;
}

/* Enhanced focus states for accessibility */
input:focus {
  outline: 2px solid #4a90e2;
  outline-offset: 2px;
}

button:focus {
  outline: 2px solid #4a90e2;
  outline-offset: 2px;
}

/* Mobile-specific styles */
@media (max-width: 768px) {
  .auth-input {
    font-size: 16px; /* Prevents zoom on iOS */
    padding: 12px;
  }

  .auth-button {
    padding: 16px 24px;
    font-size: 18px;
    min-height: 48px; /* Minimum touch target size */
  }

  /* Larger touch targets for mobile */
  button {
    min-height: 44px;
    min-width: 44px;
  }

  #passwordStrength {
    font-size: 12px;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .auth-input:focus {
    border-color: #000;
    box-shadow: 0 0 0 3px #000;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .auth-input,
  .auth-button,
  #passwordStrength .h-3 > div {
    transition: none;
  }

  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}