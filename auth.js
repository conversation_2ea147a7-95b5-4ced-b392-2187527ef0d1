// Firebase Authentication System for Neo Brutalist Component Library

import {
    signInWithEmailAndPassword,
    createUserWithEmailAndPassword,
    onAuthStateChanged,
    updateProfile
} from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js';

class AuthSystem {
    constructor() {
        this.auth = null;
        this.currentUser = null;
        this.isFirebaseReady = false;
        this.initAttempts = 0;
        this.maxInitAttempts = 50; // 5 seconds max wait
        this.init();
    }

    init() {
        console.log('🔐 Initializing Auth System...');

        // Listen for Firebase ready event
        window.addEventListener('firebaseReady', (event) => {
            console.log('✅ Firebase ready event received');
            this.auth = event.detail.auth;
            this.isFirebaseReady = true;
            this.setupFirebaseAuth();
        });

        // Listen for Firebase error event
        window.addEventListener('firebaseError', (event) => {
            console.error('❌ Firebase error event received:', event.detail.error);
            this.handleFirebaseError(event.detail.error);
        });

        // Check if Firebase is already initialized
        if (window.firebaseAuth) {
            console.log('✅ Firebase already initialized');
            this.auth = window.firebaseAuth;
            this.isFirebaseReady = true;
            this.setupFirebaseAuth();
        } else {
            // Wait for Firebase with timeout
            this.waitForFirebase();
        }
    }

    waitForFirebase() {
        this.initAttempts++;

        if (window.firebaseAuth) {
            console.log('✅ Firebase found after waiting');
            this.auth = window.firebaseAuth;
            this.isFirebaseReady = true;
            this.setupFirebaseAuth();
            return;
        }

        if (this.initAttempts >= this.maxInitAttempts) {
            console.error('❌ Firebase initialization timeout');
            this.handleFirebaseError(new Error('Firebase initialization timeout'));
            return;
        }

        setTimeout(() => this.waitForFirebase(), 100);
    }

    setupFirebaseAuth() {
        if (!this.auth) {
            console.error('❌ Auth object not available');
            return;
        }

        console.log('🔐 Setting up Firebase authentication...');

        // Listen for authentication state changes
        onAuthStateChanged(this.auth, (user) => {
            console.log('🔄 Auth state changed:', user ? 'User logged in' : 'User logged out');

            if (user) {
                this.currentUser = user;
                console.log('✅ User authenticated:', user.email);
                this.redirectToMain();
            } else {
                this.currentUser = null;
                console.log('ℹ️ No user authenticated, setting up login form');
                this.setupEventListeners();
            }
        }, (error) => {
            console.error('❌ Auth state change error:', error);
            this.handleFirebaseError(error);
        });
    }

    handleFirebaseError(error) {
        console.error('🔥 Firebase Error:', error);

        let errorMessage = 'Firebase connection failed. ';

        if (error.code) {
            switch (error.code) {
                case 'auth/network-request-failed':
                    errorMessage += 'Please check your internet connection.';
                    break;
                case 'auth/too-many-requests':
                    errorMessage += 'Too many requests. Please try again later.';
                    break;
                case 'auth/api-key-not-valid':
                    errorMessage += 'Invalid API configuration.';
                    break;
                default:
                    errorMessage += error.message;
            }
        } else {
            errorMessage += error.message || 'Unknown error occurred.';
        }

        this.showMessage(`❌ ${errorMessage}`, 'error');

        // Still setup event listeners for offline mode
        this.setupEventListeners();
    }

    setupEventListeners() {
        // Tab switching
        const signInTab = document.getElementById('signInTab');
        const signUpTab = document.getElementById('signUpTab');
        const signInForm = document.getElementById('signInForm');
        const signUpForm = document.getElementById('signUpForm');

        signInTab.addEventListener('click', () => {
            this.switchTab('signin', signInTab, signUpTab, signInForm, signUpForm);
        });

        signUpTab.addEventListener('click', () => {
            this.switchTab('signup', signInTab, signUpTab, signInForm, signUpForm);
        });

        // Form submissions
        signInForm.addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleSignIn();
        });

        signUpForm.addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleSignUp();
        });

        // Real-time password strength indicator
        const signUpPassword = document.getElementById('signUpPassword');
        if (signUpPassword) {
            signUpPassword.addEventListener('input', (e) => {
                this.updatePasswordStrength(e.target.value);
            });
        }

        // Real-time password confirmation
        const signUpConfirmPassword = document.getElementById('signUpConfirmPassword');
        if (signUpConfirmPassword) {
            signUpConfirmPassword.addEventListener('input', (e) => {
                this.updatePasswordMatch(e.target.value);
            });
        }
    }

    switchTab(tab, signInTab, signUpTab, signInForm, signUpForm) {
        if (tab === 'signin') {
            signInTab.classList.add('bg-black', 'text-white');
            signInTab.classList.remove('bg-gray-200', 'text-black');
            signUpTab.classList.add('bg-gray-200', 'text-black');
            signUpTab.classList.remove('bg-black', 'text-white');
            signInForm.classList.remove('hidden');
            signUpForm.classList.add('hidden');
        } else {
            signUpTab.classList.add('bg-black', 'text-white');
            signUpTab.classList.remove('bg-gray-200', 'text-black');
            signInTab.classList.add('bg-gray-200', 'text-black');
            signInTab.classList.remove('bg-black', 'text-white');
            signUpForm.classList.remove('hidden');
            signInForm.classList.add('hidden');
        }
        this.hideMessage();
    }

    async handleSignIn() {
        const email = document.getElementById('signInEmail').value.trim();
        const password = document.getElementById('signInPassword').value;

        // Enhanced validation
        if (!email || !password) {
            this.showMessage('Please fill in all fields', 'error');
            this.shakeForm('signInForm');
            return;
        }

        if (!this.isValidEmail(email)) {
            this.showMessage('Please enter a valid email address', 'error');
            this.highlightField('signInEmail');
            return;
        }

        // Check Firebase connection or use offline mode
        if (!this.isFirebaseReady || !this.auth) {
            if (window.offlineAuth && window.offlineAuth.isOfflineModeAvailable()) {
                return this.handleOfflineSignIn(email, password);
            } else {
                this.showMessage('❌ Firebase connection not ready. Please check your internet connection.', 'error');
                this.setFormLoading('signInForm', false);
                return;
            }
        }

        try {
            // Disable form during processing
            this.setFormLoading('signInForm', true);
            this.showMessage('🔐 Connecting to Firebase and authenticating...', 'info');

            console.log('🔄 Attempting sign in with Firebase...');
            const userCredential = await signInWithEmailAndPassword(this.auth, email, password);
            this.currentUser = userCredential.user;

            console.log('✅ Sign in successful:', userCredential.user.email);

            // Enhanced success message with user info
            const displayName = userCredential.user.displayName || email.split('@')[0];
            this.showMessage(`✅ Welcome back, ${displayName}! Redirecting to dashboard...`, 'success');

            // Add success animation
            this.showSuccessAnimation();

            // Store last login time and user info
            localStorage.setItem('lastLogin', new Date().toISOString());
            localStorage.setItem('userEmail', email);

            // Firebase auth state change will handle redirect after delay
            setTimeout(() => {
                this.redirectToMain();
            }, 2000);

        } catch (error) {
            console.error('❌ Sign in error:', error);
            let errorMessage = 'Sign in failed';

            switch (error.code) {
                case 'auth/user-not-found':
                    errorMessage = '❌ No account found with this email address. Try signing up instead.';
                    break;
                case 'auth/wrong-password':
                    errorMessage = '❌ Incorrect password. Please check your password and try again.';
                    break;
                case 'auth/invalid-email':
                    errorMessage = '❌ Invalid email address format. Please enter a valid email.';
                    break;
                case 'auth/too-many-requests':
                    errorMessage = '⚠️ Too many failed attempts. Please wait a few minutes and try again.';
                    break;
                case 'auth/user-disabled':
                    errorMessage = '🚫 This account has been disabled. Contact support for assistance.';
                    break;
                case 'auth/invalid-credential':
                    errorMessage = '❌ Invalid email or password. Please check your credentials.';
                    break;
                case 'auth/network-request-failed':
                    errorMessage = '🌐 Network error. Please check your internet connection and try again.';
                    break;
                case 'auth/timeout':
                    errorMessage = '⏱️ Request timeout. Please try again.';
                    break;
                default:
                    errorMessage = `❌ Authentication failed: ${error.message}`;
            }

            this.showMessage(errorMessage, 'error');
            this.shakeForm('signInForm');
            this.setFormLoading('signInForm', false);
        }
    }

    async handleSignUp() {
        const name = document.getElementById('signUpName').value.trim();
        const email = document.getElementById('signUpEmail').value.trim();
        const password = document.getElementById('signUpPassword').value;
        const confirmPassword = document.getElementById('signUpConfirmPassword').value;

        // Enhanced validation
        if (!name || !email || !password || !confirmPassword) {
            this.showMessage('Please fill in all required fields', 'error');
            this.shakeForm('signUpForm');
            return;
        }

        if (name.length < 2) {
            this.showMessage('Name must be at least 2 characters long', 'error');
            this.highlightField('signUpName');
            return;
        }

        if (!this.isValidEmail(email)) {
            this.showMessage('Please enter a valid email address', 'error');
            this.highlightField('signUpEmail');
            return;
        }

        if (password !== confirmPassword) {
            this.showMessage('Passwords do not match', 'error');
            this.highlightField('signUpPassword');
            this.highlightField('signUpConfirmPassword');
            return;
        }

        if (!this.isStrongPassword(password)) {
            this.showMessage('Password must be at least 8 characters with uppercase, lowercase, number, and special character', 'error');
            this.highlightField('signUpPassword');
            return;
        }

        // Check Firebase connection or use offline mode
        if (!this.isFirebaseReady || !this.auth) {
            if (window.offlineAuth && window.offlineAuth.isOfflineModeAvailable()) {
                return this.handleOfflineSignUp(name, email, password);
            } else {
                this.showMessage('❌ Firebase connection not ready. Please check your internet connection.', 'error');
                this.setFormLoading('signUpForm', false);
                return;
            }
        }

        try {
            // Disable form during processing
            this.setFormLoading('signUpForm', true);
            this.showMessage('🔐 Connecting to Firebase and creating your account...', 'info');

            console.log('🔄 Attempting account creation with Firebase...');
            const userCredential = await createUserWithEmailAndPassword(this.auth, email, password);

            console.log('✅ Account created successfully:', userCredential.user.email);

            // Update the user's display name
            console.log('🔄 Updating user profile...');
            await updateProfile(userCredential.user, {
                displayName: name
            });

            console.log('✅ User profile updated');

            this.currentUser = userCredential.user;

            // Enhanced success message
            this.showMessage(`🎉 Welcome to Wolf CTF Community, ${name}! Your account has been created successfully!`, 'success');

            // Add success animation
            this.showSuccessAnimation();

            // Store user preferences and info
            localStorage.setItem('userPreferences', JSON.stringify({
                displayName: name,
                email: email,
                joinDate: new Date().toISOString(),
                theme: 'neo-brutalist',
                uid: userCredential.user.uid
            }));

            localStorage.setItem('userEmail', email);
            localStorage.setItem('lastLogin', new Date().toISOString());

            // Send welcome notification (simulated)
            setTimeout(() => {
                this.showMessage('📧 Welcome email sent! Check your inbox for getting started tips.', 'info');
            }, 1000);

            // Firebase auth state change will handle redirect after delay
            setTimeout(() => {
                this.redirectToMain();
            }, 3000);

        } catch (error) {
            console.error('❌ Sign up error:', error);
            let errorMessage = 'Account creation failed';

            switch (error.code) {
                case 'auth/email-already-in-use':
                    errorMessage = '❌ An account with this email already exists. Try signing in instead.';
                    // Suggest switching to sign-in
                    setTimeout(() => {
                        const signInTab = document.getElementById('signInTab');
                        if (signInTab) {
                            signInTab.click();
                            document.getElementById('signInEmail').value = email;
                        }
                    }, 2000);
                    break;
                case 'auth/invalid-email':
                    errorMessage = '❌ Invalid email address format. Please enter a valid email.';
                    break;
                case 'auth/weak-password':
                    errorMessage = '❌ Password is too weak. Please choose a stronger password with at least 6 characters.';
                    break;
                case 'auth/operation-not-allowed':
                    errorMessage = '❌ Account creation is currently disabled. Please contact support.';
                    break;
                case 'auth/network-request-failed':
                    errorMessage = '🌐 Network error. Please check your internet connection and try again.';
                    break;
                case 'auth/timeout':
                    errorMessage = '⏱️ Request timeout. Please try again.';
                    break;
                default:
                    errorMessage = `❌ Account creation failed: ${error.message}`;
            }

            this.showMessage(errorMessage, 'error');
            this.shakeForm('signUpForm');
            this.setFormLoading('signUpForm', false);
        }
    }

    showMessage(message, type) {
        const messageContainer = document.getElementById('messageContainer');
        const messageBox = document.getElementById('messageBox');

        messageBox.innerHTML = message; // Changed to innerHTML to support emojis and icons

        let bgColor, textColor, additionalClasses = '';
        switch (type) {
            case 'success':
                bgColor = 'bg-green-400';
                textColor = 'text-black';
                additionalClasses = 'message-success';
                break;
            case 'info':
                bgColor = 'bg-blue-400';
                textColor = 'text-white';
                additionalClasses = 'pulse';
                break;
            case 'error':
            default:
                bgColor = 'bg-red-400';
                textColor = 'text-white';
                additionalClasses = 'shake';
                break;
        }

        messageBox.className = `p-4 border-4 border-black font-bold text-center uppercase tracking-wide ${bgColor} ${textColor} ${additionalClasses}`;

        messageContainer.classList.remove('hidden');

        // Add entrance animation
        messageContainer.style.animation = 'slideInRight 0.3s ease-out';

        // Auto-hide messages based on type
        let hideDelay = 5000; // Default 5 seconds
        if (type === 'success') {
            hideDelay = 8000; // Success messages stay longer
        } else if (type === 'info') {
            hideDelay = 4000; // Info messages hide faster
        }

        setTimeout(() => {
            this.hideMessage();
        }, hideDelay);
    }

    hideMessage() {
        const messageContainer = document.getElementById('messageContainer');
        messageContainer.classList.add('hidden');
    }

    redirectToMain() {
        window.location.href = 'main.html';
    }

    // Handle offline sign-in
    async handleOfflineSignIn(email, password) {
        try {
            this.showMessage('🔄 Signing in offline mode...', 'info');

            const userCredential = window.offlineAuth.authenticateOfflineUser(email, password);
            this.currentUser = userCredential.user;

            this.showMessage('✅ Signed in successfully (Offline Mode)', 'success');
            this.showSuccessAnimation();

            // Store offline session
            localStorage.setItem('offlineSession', JSON.stringify({
                user: userCredential.user,
                loginTime: new Date().toISOString(),
                isOffline: true
            }));

            setTimeout(() => {
                this.redirectToMain();
            }, 2000);

        } catch (error) {
            console.error('Offline sign in error:', error);
            this.showMessage(`❌ ${error.message}`, 'error');
            this.shakeForm('signInForm');
        } finally {
            this.setFormLoading('signInForm', false);
        }
    }

    // Handle offline sign-up
    async handleOfflineSignUp(name, email, password) {
        try {
            this.showMessage('🔄 Creating account in offline mode...', 'info');

            const userCredential = window.offlineAuth.createOfflineAccount(name, email, password);
            this.currentUser = userCredential.user;

            this.showMessage(`🎉 Welcome ${name}! Account created (Offline Mode)`, 'success');
            this.showSuccessAnimation();

            // Store offline session
            localStorage.setItem('offlineSession', JSON.stringify({
                user: userCredential.user,
                loginTime: new Date().toISOString(),
                isOffline: true
            }));

            // Show offline mode info
            setTimeout(() => {
                this.showMessage('📱 Your account will sync when internet is restored', 'info');
            }, 1000);

            setTimeout(() => {
                this.redirectToMain();
            }, 3000);

        } catch (error) {
            console.error('Offline sign up error:', error);
            this.showMessage(`❌ Account creation failed: ${error.message}`, 'error');
            this.shakeForm('signUpForm');
        } finally {
            this.setFormLoading('signUpForm', false);
        }
    }

    // Email validation
    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    // Strong password validation
    isStrongPassword(password) {
        const requirements = this.getPasswordRequirements(password);
        const essentialRequirements = requirements.slice(0, 5); // First 5 are essential

        // Check if all essential requirements are met
        const allEssentialMet = essentialRequirements.every(req => req.met);

        // Additional check for minimum length
        const hasMinimumLength = password.length >= 6;

        return allEssentialMet && hasMinimumLength;
    }

    // Highlight field with error
    highlightField(fieldId) {
        const field = document.getElementById(fieldId);
        if (field) {
            field.style.borderColor = '#ff4444';
            field.style.boxShadow = '0 0 0 3px rgba(255, 68, 68, 0.3)';

            // Remove highlight after 3 seconds
            setTimeout(() => {
                field.style.borderColor = '#000';
                field.style.boxShadow = 'none';
            }, 3000);
        }
    }

    // Shake form animation
    shakeForm(formId) {
        const form = document.getElementById(formId);
        if (form) {
            form.style.animation = 'shake 0.5s ease-in-out';
            setTimeout(() => {
                form.style.animation = '';
            }, 500);
        }
    }

    // Set form loading state
    setFormLoading(formId, isLoading) {
        const form = document.getElementById(formId);
        const submitButton = form.querySelector('button[type="submit"]');
        const inputs = form.querySelectorAll('input');

        if (isLoading) {
            submitButton.disabled = true;
            submitButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>PROCESSING...';
            inputs.forEach(input => input.disabled = true);
        } else {
            submitButton.disabled = false;
            inputs.forEach(input => input.disabled = false);

            // Restore original button text
            if (formId === 'signInForm') {
                submitButton.innerHTML = '<i class="fas fa-sign-in-alt mr-2"></i>SIGN IN';
            } else {
                submitButton.innerHTML = '<i class="fas fa-user-plus mr-2"></i>SIGN UP';
            }
        }
    }

    // Show success animation
    showSuccessAnimation() {
        const successOverlay = document.createElement('div');
        successOverlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 255, 0, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            animation: successPulse 2s ease-in-out;
        `;

        successOverlay.innerHTML = `
            <div style="text-align: center; color: #00ff00; font-size: 64px;">
                <i class="fas fa-check-circle" style="animation: successBounce 1s ease-in-out;"></i>
            </div>
        `;

        document.body.appendChild(successOverlay);

        setTimeout(() => {
            if (document.body.contains(successOverlay)) {
                document.body.removeChild(successOverlay);
            }
        }, 2000);
    }

    // Update password strength indicator
    updatePasswordStrength(password) {
        let strengthIndicator = document.getElementById('passwordStrength');

        // Create strength indicator if it doesn't exist
        if (!strengthIndicator) {
            strengthIndicator = document.createElement('div');
            strengthIndicator.id = 'passwordStrength';
            strengthIndicator.className = 'mt-2 text-sm font-mono';

            const passwordField = document.getElementById('signUpPassword');
            passwordField.parentNode.appendChild(strengthIndicator);
        }

        if (password.length === 0) {
            strengthIndicator.innerHTML = '';
            return;
        }

        const strength = this.calculatePasswordStrength(password);
        let strengthText, strengthColor, strengthBg, progressWidth;

        switch (strength.level) {
            case 0:
                strengthText = 'Very Weak';
                strengthColor = 'text-red-600';
                strengthBg = 'bg-red-500';
                progressWidth = 20;
                break;
            case 1:
                strengthText = 'Weak';
                strengthColor = 'text-red-500';
                strengthBg = 'bg-red-400';
                progressWidth = 30;
                break;
            case 2:
                strengthText = 'Fair';
                strengthColor = 'text-yellow-600';
                strengthBg = 'bg-yellow-400';
                progressWidth = 50;
                break;
            case 3:
                strengthText = 'Good';
                strengthColor = 'text-blue-600';
                strengthBg = 'bg-blue-400';
                progressWidth = 75;
                break;
            case 4:
                strengthText = 'Strong';
                strengthColor = 'text-green-600';
                strengthBg = 'bg-green-400';
                progressWidth = 90;
                break;
            case 5:
                strengthText = 'Very Strong';
                strengthColor = 'text-green-700';
                strengthBg = 'bg-green-500';
                progressWidth = 100;
                break;
        }

        // Create requirements checklist
        const requirements = this.getPasswordRequirements(password);
        const requirementsList = requirements.map(req =>
            `<div class="flex items-center gap-1 text-xs">
                <span class="${req.met ? 'text-green-600' : 'text-red-600'}">${req.met ? '✓' : '✗'}</span>
                <span class="${req.met ? 'text-green-600' : 'text-gray-600'}">${req.text}</span>
            </div>`
        ).join('');

        strengthIndicator.innerHTML = `
            <div class="border-2 border-black p-3 bg-white">
                <div class="flex items-center gap-2 mb-2">
                    <div class="flex-1 h-3 bg-gray-200 border-2 border-black">
                        <div class="${strengthBg} h-full transition-all duration-500 border-r-2 border-black" style="width: ${progressWidth}%"></div>
                    </div>
                    <span class="${strengthColor} font-bold text-sm">${strengthText}</span>
                </div>
                <div class="space-y-1">
                    ${requirementsList}
                </div>
                ${strength.score >= 4 ? '<div class="text-xs text-green-600 font-bold mt-2">🎉 Password meets all requirements!</div>' : ''}
            </div>
        `;
    }

    // Calculate password strength
    calculatePasswordStrength(password) {
        let score = 0;
        const feedback = [];

        // Length check (minimum 6, recommended 8+)
        if (password.length >= 12) {
            score += 2; // Bonus for very long passwords
        } else if (password.length >= 8) {
            score += 1;
        } else if (password.length >= 6) {
            score += 0.5;
        }

        // Character type checks
        if (/[a-z]/.test(password)) {
            score++;
        }

        if (/[A-Z]/.test(password)) {
            score++;
        }

        if (/\d/.test(password)) {
            score++;
        }

        if (/[@$!%*?&#+\-_=<>]/.test(password)) {
            score++;
        }

        // Bonus points for complexity
        if (/[a-z].*[A-Z]|[A-Z].*[a-z]/.test(password)) {
            score += 0.5; // Mixed case
        }

        if (/\d.*[@$!%*?&#+\-_=<>]|[@$!%*?&#+\-_=<>].*\d/.test(password)) {
            score += 0.5; // Numbers and symbols mixed
        }

        // Penalty for common patterns
        if (/(.)\1{2,}/.test(password)) {
            score -= 0.5; // Repeated characters
        }

        if (/123|abc|qwe|password|admin/i.test(password)) {
            score -= 1; // Common patterns
        }

        return {
            level: Math.min(Math.floor(score), 5),
            score: score,
            feedback: feedback
        };
    }

    // Get detailed password requirements
    getPasswordRequirements(password) {
        return [
            {
                text: 'At least 6 characters (8+ recommended)',
                met: password.length >= 6,
                recommended: password.length >= 8
            },
            {
                text: 'Contains lowercase letter (a-z)',
                met: /[a-z]/.test(password)
            },
            {
                text: 'Contains uppercase letter (A-Z)',
                met: /[A-Z]/.test(password)
            },
            {
                text: 'Contains number (0-9)',
                met: /\d/.test(password)
            },
            {
                text: 'Contains special character (@$!%*?&#+)',
                met: /[@$!%*?&#+\-_=<>]/.test(password)
            },
            {
                text: 'No repeated characters (aaa, 111)',
                met: !/(.)\1{2,}/.test(password)
            },
            {
                text: 'No common patterns (123, abc, password)',
                met: !/123|abc|qwe|password|admin|login|user/i.test(password)
            }
        ];
    }

    // Update password match indicator
    updatePasswordMatch(confirmPassword) {
        const password = document.getElementById('signUpPassword').value;
        let matchIndicator = document.getElementById('passwordMatch');

        // Create match indicator if it doesn't exist
        if (!matchIndicator) {
            matchIndicator = document.createElement('div');
            matchIndicator.id = 'passwordMatch';
            matchIndicator.className = 'mt-2 text-sm font-mono';

            const confirmField = document.getElementById('signUpConfirmPassword');
            confirmField.parentNode.appendChild(matchIndicator);
        }

        if (confirmPassword.length === 0) {
            matchIndicator.innerHTML = '';
            return;
        }

        if (password === confirmPassword) {
            matchIndicator.innerHTML = `
                <div class="text-green-600 font-bold">
                    <i class="fas fa-check mr-2"></i>Passwords match!
                </div>
            `;
        } else {
            matchIndicator.innerHTML = `
                <div class="text-red-600 font-bold">
                    <i class="fas fa-times mr-2"></i>Passwords do not match
                </div>
            `;
        }
    }
}

// Initialize the authentication system when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new AuthSystem();
});
