<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Wolf CTF Community - Component Library</title>

  <!-- Security Meta Tags (Client-side only) -->
  <meta name="referrer" content="no-referrer">
  <meta name="robots" content="noindex, nofollow, noarchive, nosnippet, noimageindex">
  <meta name="googlebot" content="noindex, nofollow, noarchive, nosnippet, noimageindex">
  <meta name="format-detection" content="telephone=no, email=no, address=no">
  <!-- Note: X-Frame-Options, CSP, and other security headers must be set server-side -->

  <!-- Tailwind CSS Production Build -->
  <link rel="stylesheet" href="./tailwind-production.css">
  <!-- Google Fonts for bold, brutalist typography -->
  <link href="https://fonts.googleapis.com/css2?family=Bebas+Neue&family=Roboto+Mono:wght@400;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="./style.css">

  <!-- Firebase SDK -->
  <script type="module">
    import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js';
    import { getAuth } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js';

    // For Firebase JS SDK v7.20.0 and later, measurementId is optional
    const firebaseConfig = {
      apiKey: "AIzaSyDN0E7ombncbYj-_iLhcEYxUGHb1FWo-6E",
      authDomain: "cyber-wolf-community-ctf.firebaseapp.com",
      projectId: "cyber-wolf-community-ctf",
      storageBucket: "cyber-wolf-community-ctf.firebasestorage.app",
      messagingSenderId: "370646269039",
      appId: "1:370646269039:web:e0566975c79d91d669219a",
      measurementId: "G-NH35HDL4N6"
    };

    try {
      console.log('🔥 Initializing Firebase for main page...');
      const app = initializeApp(firebaseConfig);
      const auth = getAuth(app);

      // Make auth available globally
      window.firebaseAuth = auth;
      window.firebaseApp = app;

      // Dispatch custom event when Firebase is ready
      window.dispatchEvent(new CustomEvent('firebaseReady', {
        detail: { auth, app }
      }));

      console.log('✅ Firebase initialized successfully for main page');
    } catch (error) {
      console.error('❌ Firebase initialization failed on main page:', error);
      window.dispatchEvent(new CustomEvent('firebaseError', {
        detail: { error }
      }));
    }
  </script>
</head>
<body class="p-4 md:p-8">
  <!-- Header with User Info and Logout -->
  <div class="container mx-auto mb-6">
    <div class="flex justify-between items-center mb-6">
      <div>
        <h1 class="text-4xl md:text-5xl text-black">Neo Brutalist Component Library</h1>
        <p class="text-lg text-gray-700 font-mono mt-2">Welcome back, <span id="userName" class="font-bold text-black">User</span>!</p>
      </div>
      <button id="logoutBtn" 
              class="py-3 px-6 bg-red-500 text-white font-bold text-lg border-4 border-black shadow-[4px_4px_0_#000] hover:shadow-[2px_2px_0_#000] hover:translate-x-[2px] hover:translate-y-[2px] transition-all duration-200 uppercase tracking-wide">
        <i class="fas fa-sign-out-alt mr-2"></i>LOGOUT
      </button>
    </div>

    <!-- Component Grid -->
    <div id="components" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-2 gap-6">
      <!-- Components will be injected here by JavaScript -->
    </div>
  </div>
  
  <!-- Footer Link -->
  <a target="_blank" href="https://www.rustcodeweb.com/" 
     style="position: fixed; bottom: 0.6rem; right: 0.6rem; background: #FFD166; color: #073B4C; text-decoration: none; padding: 0.5rem 1rem; border: 3px solid #073B4C; box-shadow: 3px 3px 0 #073B4C; font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif; font-weight: 600; font-size: 0.875rem; transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); z-index: 1200; border-radius: 2px; display: flex; align-items: center; gap: 0.5rem;"
     onmouseover="this.style.background='#F8F9FA'; this.style.transform='translate(-1px, -1px)'; this.style.boxShadow='4px 4px 0 #073B4C';"
     onmouseout="this.style.background='#FFD166'; this.style.transform='translate(0, 0)'; this.style.boxShadow='3px 3px 0 #073B4C';"
     onmousedown="this.style.transform='translate(0, 0)'; this.style.boxShadow='2px 2px 0 #073B4C';"
     onmouseup="this.style.transform='translate(-1px, -1px)'; this.style.boxShadow='4px 4px 0 #073B4C';"
     aria-label="Visit rustcodeweb.com">
     <i class="fas fa-link" style="font-size: 1rem;"></i>
  </a>

  <script src="./fetch-manager.js"></script>
  <script src="./error-handler.js"></script>
  <script src="./offline-auth.js"></script>
  <script src="./network-diagnostics.js"></script>
  <script src="./firebase-status.js"></script>
  <script src="./security.js"></script>
  <script src="./security-monitor.js"></script>
  <script src="./script.js"></script>
  <script type="module" src="./main.js"></script>
</body>
</html>
