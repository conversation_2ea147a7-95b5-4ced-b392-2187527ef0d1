// Centralized Fetch Manager for Wolf CTF Community
// Prevents fetch override conflicts between different systems

class FetchManager {
    constructor() {
        this.originalFetch = window.fetch;
        this.interceptors = [];
        this.isInitialized = false;
        this.init();
    }

    init() {
        if (this.isInitialized) return;
        
        // Store original fetch globally
        window._originalFetch = this.originalFetch;
        
        // Override fetch with centralized handler
        window.fetch = (...args) => this.handleFetch(...args);
        
        this.isInitialized = true;
        console.log('🔧 Fetch Manager initialized');
    }

    // Add interceptor
    addInterceptor(name, interceptor) {
        this.interceptors.push({
            name,
            interceptor,
            priority: interceptor.priority || 0
        });
        
        // Sort by priority (higher priority first)
        this.interceptors.sort((a, b) => b.priority - a.priority);
        
        console.log(`🔧 Added fetch interceptor: ${name}`);
    }

    // Handle fetch with all interceptors
    async handleFetch(...args) {
        let url = args[0];
        let options = args[1] || {};
        
        // Apply interceptors in priority order
        for (const { name, interceptor } of this.interceptors) {
            try {
                const result = await interceptor.beforeRequest?.(url, options, args);
                
                // If interceptor returns false, block the request
                if (result === false) {
                    throw new Error(`Request blocked by ${name} interceptor`);
                }
                
                // If interceptor returns modified args, use them
                if (result && Array.isArray(result)) {
                    args = result;
                    url = args[0];
                    options = args[1] || {};
                }
            } catch (error) {
                // If any interceptor throws, handle it
                for (const { interceptor: errorInterceptor } of this.interceptors) {
                    errorInterceptor.onError?.(error, url, options);
                }
                throw error;
            }
        }

        try {
            // Make the actual request
            const response = await this.originalFetch(...args);
            
            // Apply after-response interceptors
            for (const { interceptor } of this.interceptors) {
                interceptor.afterResponse?.(response, url, options);
            }
            
            return response;
        } catch (error) {
            // Handle fetch errors
            for (const { interceptor } of this.interceptors) {
                interceptor.onError?.(error, url, options);
            }
            throw error;
        }
    }

    // Remove interceptor
    removeInterceptor(name) {
        this.interceptors = this.interceptors.filter(i => i.name !== name);
        console.log(`🔧 Removed fetch interceptor: ${name}`);
    }

    // Get original fetch (for direct use)
    getOriginalFetch() {
        return this.originalFetch;
    }

    // Reset to original fetch
    reset() {
        window.fetch = this.originalFetch;
        this.interceptors = [];
        this.isInitialized = false;
        console.log('🔧 Fetch Manager reset');
    }
}

// Initialize fetch manager
const fetchManager = new FetchManager();

// Make it globally available
window.fetchManager = fetchManager;

// Security Monitor Interceptor
fetchManager.addInterceptor('security-monitor', {
    priority: 100, // Highest priority for security
    beforeRequest: (url, options) => {
        // Log network request
        if (window.securityMonitor) {
            window.securityMonitor.logNetworkRequest?.('FETCH', url);
            
            // Check for suspicious URLs
            if (window.securityMonitor.isSuspiciousURL?.(url)) {
                window.securityMonitor.reportThreat?.(`Suspicious fetch request to: ${url}`, 'NETWORK_THREAT');
                return false; // Block request
            }
        }
        return true; // Allow request
    }
});

// Error Handler Interceptor
fetchManager.addInterceptor('error-handler', {
    priority: 90,
    afterResponse: (response, url) => {
        if (!response.ok && window.errorHandler) {
            window.errorHandler.handleError?.({
                type: 'Network Error',
                message: `HTTP ${response.status}: ${response.statusText}`,
                url: url,
                timestamp: new Date().toISOString()
            });
        }
    },
    onError: (error, url) => {
        if (window.errorHandler) {
            window.errorHandler.handleError?.({
                type: 'Network Error',
                message: error.message,
                url: url,
                timestamp: new Date().toISOString()
            });
        }
    }
});

// Connection Quality Interceptor
fetchManager.addInterceptor('connection-quality', {
    priority: 10,
    beforeRequest: (url, options) => {
        // Add timestamp for latency measurement
        options._startTime = Date.now();
        return [url, options];
    },
    afterResponse: (response, url, options) => {
        if (options._startTime && window.offlineAuth) {
            const latency = Date.now() - options._startTime;
            
            // Update connection quality based on latency
            if (latency > 5000) {
                window.offlineAuth.updateConnectionStatus?.('slow', 'Slow Connection');
            } else if (latency > 2000) {
                window.offlineAuth.updateConnectionStatus?.('moderate', 'Moderate Connection');
            } else if (latency < 1000) {
                window.offlineAuth.updateConnectionStatus?.('good', 'Good Connection');
            }
        }
    }
});

// Safe fetch function for internal use (bypasses interceptors)
window.safeFetch = (...args) => {
    return fetchManager.getOriginalFetch()(...args);
};

// Export for use in other modules
window.FetchManager = FetchManager;

console.log('🔧 Centralized Fetch Manager loaded with interceptors');
