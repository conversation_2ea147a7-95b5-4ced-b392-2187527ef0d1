// Network Diagnostics Tool for Wolf CTF Community
// Helps diagnose and fix internet connectivity issues

class NetworkDiagnostics {
    constructor() {
        this.diagnosticResults = [];
        this.isRunning = false;
        this.init();
    }

    init() {
        // Add diagnostic button to page
        this.createDiagnosticButton();
        
        // Auto-run diagnostics if connection issues detected
        this.setupAutoDetection();
    }

    createDiagnosticButton() {
        const button = document.createElement('button');
        button.id = 'network-diagnostic-btn';
        button.innerHTML = '🔧 Network Diagnostics';
        button.style.cssText = `
            position: fixed;
            top: 50px;
            left: 10px;
            background: #0066ff;
            color: white;
            border: 2px solid #000;
            padding: 8px 12px;
            font-family: 'Roboto Mono', monospace;
            font-size: 11px;
            cursor: pointer;
            z-index: 9997;
            border-radius: 4px;
            transition: all 0.3s ease;
        `;
        
        button.addEventListener('click', () => {
            this.runDiagnostics();
        });
        
        button.addEventListener('mouseenter', () => {
            button.style.background = '#0044cc';
        });
        
        button.addEventListener('mouseleave', () => {
            button.style.background = '#0066ff';
        });
        
        document.body.appendChild(button);
    }

    setupAutoDetection() {
        // Run diagnostics when Firebase fails to connect
        window.addEventListener('firebaseError', () => {
            setTimeout(() => {
                if (!window.firebaseAuth) {
                    this.runDiagnostics();
                }
            }, 5000);
        });
    }

    async runDiagnostics() {
        if (this.isRunning) return;
        
        this.isRunning = true;
        this.diagnosticResults = [];
        
        const modal = this.createDiagnosticModal();
        document.body.appendChild(modal);
        
        try {
            await this.performDiagnostics(modal);
        } catch (error) {
            console.error('Diagnostic error:', error);
        } finally {
            this.isRunning = false;
        }
    }

    createDiagnosticModal() {
        const modal = document.createElement('div');
        modal.id = 'diagnostic-modal';
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10002;
            font-family: 'Roboto Mono', monospace;
        `;

        modal.innerHTML = `
            <div style="background: #000; color: #00ff00; padding: 30px; border: 4px solid #00ff00; box-shadow: 0 0 20px rgba(0,255,0,0.5); max-width: 600px; width: 90%; max-height: 80vh; overflow-y: auto;">
                <div style="display: flex; justify-content: between; align-items: center; margin-bottom: 20px;">
                    <h2 style="font-size: 20px; margin: 0;">🔧 Network Diagnostics</h2>
                    <button id="close-diagnostic" style="background: #ff4444; color: white; border: 2px solid #fff; padding: 5px 10px; cursor: pointer; margin-left: auto;">×</button>
                </div>
                <div id="diagnostic-progress" style="margin-bottom: 20px;">
                    <div style="background: #333; height: 20px; border: 2px solid #00ff00;">
                        <div id="progress-bar" style="background: #00ff00; height: 100%; width: 0%; transition: width 0.3s ease;"></div>
                    </div>
                    <div id="current-test" style="margin-top: 10px; font-size: 14px;">Initializing diagnostics...</div>
                </div>
                <div id="diagnostic-results" style="font-size: 12px; line-height: 1.4;"></div>
                <div id="diagnostic-actions" style="margin-top: 20px; display: none;">
                    <button id="retry-connection" style="background: #00aa00; color: white; border: 2px solid #fff; padding: 10px 15px; margin-right: 10px; cursor: pointer;">🔄 Retry Connection</button>
                    <button id="use-offline" style="background: #ff6600; color: white; border: 2px solid #fff; padding: 10px 15px; cursor: pointer;">📱 Use Offline Mode</button>
                </div>
            </div>
        `;

        // Handle close button
        modal.querySelector('#close-diagnostic').addEventListener('click', () => {
            document.body.removeChild(modal);
        });

        return modal;
    }

    async performDiagnostics(modal) {
        const progressBar = modal.querySelector('#progress-bar');
        const currentTest = modal.querySelector('#current-test');
        const resultsDiv = modal.querySelector('#diagnostic-results');
        const actionsDiv = modal.querySelector('#diagnostic-actions');

        const tests = [
            { name: 'Basic Connectivity', test: () => this.testBasicConnectivity() },
            { name: 'DNS Resolution', test: () => this.testDNSResolution() },
            { name: 'Firebase Endpoints', test: () => this.testFirebaseEndpoints() },
            { name: 'CDN Access', test: () => this.testCDNAccess() },
            { name: 'Latency Test', test: () => this.testLatency() },
            { name: 'Browser Compatibility', test: () => this.testBrowserCompatibility() }
        ];

        for (let i = 0; i < tests.length; i++) {
            const test = tests[i];
            currentTest.textContent = `Running: ${test.name}...`;
            progressBar.style.width = `${((i + 1) / tests.length) * 100}%`;

            try {
                const result = await test.test();
                this.diagnosticResults.push({
                    name: test.name,
                    status: result.success ? 'PASS' : 'FAIL',
                    message: result.message,
                    details: result.details || ''
                });
            } catch (error) {
                this.diagnosticResults.push({
                    name: test.name,
                    status: 'ERROR',
                    message: error.message,
                    details: ''
                });
            }

            this.updateResults(resultsDiv);
            await this.delay(500); // Small delay between tests
        }

        currentTest.textContent = 'Diagnostics Complete';
        this.showRecommendations(modal);
        actionsDiv.style.display = 'block';

        // Handle action buttons
        modal.querySelector('#retry-connection').addEventListener('click', () => {
            window.location.reload();
        });

        modal.querySelector('#use-offline').addEventListener('click', () => {
            if (window.offlineAuth) {
                window.offlineAuth.updateConnectionStatus('offline', 'Offline Mode Activated');
            }
            document.body.removeChild(modal);
        });
    }

    updateResults(resultsDiv) {
        resultsDiv.innerHTML = this.diagnosticResults.map(result => {
            const icon = result.status === 'PASS' ? '✅' : result.status === 'FAIL' ? '❌' : '⚠️';
            const color = result.status === 'PASS' ? '#00ff00' : result.status === 'FAIL' ? '#ff4444' : '#ffaa00';
            
            return `
                <div style="margin-bottom: 10px; padding: 8px; border: 1px solid ${color}; background: rgba(${result.status === 'PASS' ? '0,255,0' : result.status === 'FAIL' ? '255,68,68' : '255,170,0'}, 0.1);">
                    <div style="color: ${color}; font-weight: bold;">${icon} ${result.name}: ${result.status}</div>
                    <div style="color: #ccc; font-size: 11px; margin-top: 4px;">${result.message}</div>
                    ${result.details ? `<div style="color: #999; font-size: 10px; margin-top: 2px;">${result.details}</div>` : ''}
                </div>
            `;
        }).join('');
    }

    async testBasicConnectivity() {
        try {
            // Use safe fetch to avoid recursion
            await window.safeFetch('https://www.google.com/favicon.ico', {
                method: 'HEAD',
                mode: 'no-cors',
                cache: 'no-cache'
            });
            return {
                success: true,
                message: 'Internet connection is working'
            };
        } catch (error) {
            return {
                success: false,
                message: 'No internet connection detected',
                details: error.message
            };
        }
    }

    async testDNSResolution() {
        try {
            const startTime = Date.now();
            await window.safeFetch('https://8.8.8.8', { method: 'HEAD', mode: 'no-cors' });
            const endTime = Date.now();

            return {
                success: true,
                message: `DNS resolution working (${endTime - startTime}ms)`
            };
        } catch (error) {
            return {
                success: false,
                message: 'DNS resolution issues detected',
                details: 'Try using different DNS servers (8.8.8.8, 1.1.1.1)'
            };
        }
    }

    async testFirebaseEndpoints() {
        try {
            await window.safeFetch('https://firebase.googleapis.com/', {
                method: 'HEAD',
                mode: 'no-cors'
            });
            return {
                success: true,
                message: 'Firebase endpoints accessible'
            };
        } catch (error) {
            return {
                success: false,
                message: 'Firebase endpoints blocked or unreachable',
                details: 'Check firewall, proxy, or ISP restrictions'
            };
        }
    }

    async testCDNAccess() {
        try {
            await window.safeFetch('https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js', {
                method: 'HEAD',
                mode: 'no-cors'
            });
            return {
                success: true,
                message: 'CDN access working'
            };
        } catch (error) {
            return {
                success: false,
                message: 'CDN access blocked',
                details: 'Firebase CDN may be blocked by firewall or ad blocker'
            };
        }
    }

    async testLatency() {
        try {
            const startTime = Date.now();
            await window.safeFetch('https://www.google.com/favicon.ico', {
                method: 'HEAD',
                mode: 'no-cors'
            });
            const latency = Date.now() - startTime;

            let quality = 'Excellent';
            if (latency > 2000) quality = 'Poor';
            else if (latency > 1000) quality = 'Fair';
            else if (latency > 500) quality = 'Good';

            return {
                success: latency < 5000,
                message: `Network latency: ${latency}ms (${quality})`
            };
        } catch (error) {
            return {
                success: false,
                message: 'Unable to measure latency'
            };
        }
    }

    testBrowserCompatibility() {
        const features = {
            'ES6 Modules': typeof import !== 'undefined',
            'Fetch API': typeof fetch !== 'undefined',
            'Local Storage': typeof localStorage !== 'undefined',
            'Service Workers': 'serviceWorker' in navigator,
            'WebSockets': typeof WebSocket !== 'undefined'
        };

        const unsupported = Object.entries(features)
            .filter(([name, supported]) => !supported)
            .map(([name]) => name);

        return {
            success: unsupported.length === 0,
            message: unsupported.length === 0 
                ? 'Browser fully compatible' 
                : `Missing features: ${unsupported.join(', ')}`,
            details: unsupported.length > 0 ? 'Consider updating your browser' : ''
        };
    }

    showRecommendations(modal) {
        const failedTests = this.diagnosticResults.filter(r => r.status === 'FAIL');
        
        if (failedTests.length === 0) {
            return;
        }

        const recommendations = document.createElement('div');
        recommendations.style.cssText = `
            margin-top: 20px;
            padding: 15px;
            border: 2px solid #ffaa00;
            background: rgba(255, 170, 0, 0.1);
        `;

        let recommendationText = '<div style="color: #ffaa00; font-weight: bold; margin-bottom: 10px;">🔧 Recommendations:</div>';
        
        if (failedTests.some(t => t.name === 'Basic Connectivity')) {
            recommendationText += '<div style="margin-bottom: 5px;">• Check your internet connection</div>';
            recommendationText += '<div style="margin-bottom: 5px;">• Try restarting your router/modem</div>';
        }
        
        if (failedTests.some(t => t.name === 'Firebase Endpoints')) {
            recommendationText += '<div style="margin-bottom: 5px;">• Disable VPN or proxy temporarily</div>';
            recommendationText += '<div style="margin-bottom: 5px;">• Check firewall settings</div>';
        }
        
        if (failedTests.some(t => t.name === 'CDN Access')) {
            recommendationText += '<div style="margin-bottom: 5px;">• Disable ad blockers for this site</div>';
            recommendationText += '<div style="margin-bottom: 5px;">• Try incognito/private browsing mode</div>';
        }

        recommendationText += '<div style="margin-top: 10px; color: #00ff00;">• Use Offline Mode as a temporary solution</div>';

        recommendations.innerHTML = recommendationText;
        modal.querySelector('#diagnostic-results').appendChild(recommendations);
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// Initialize network diagnostics
document.addEventListener('DOMContentLoaded', () => {
    window.networkDiagnostics = new NetworkDiagnostics();
});

console.log('🔧 Network Diagnostics loaded. Click the diagnostic button or it will auto-run on connection issues.');
