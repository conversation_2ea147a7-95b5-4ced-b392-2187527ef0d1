// Firebase Main page functionality for Neo Brutalist Component Library

import {
    onAuthStateChanged,
    signOut
} from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js';

class MainPageManager {
    constructor() {
        this.auth = window.firebaseAuth;
        this.currentUser = null;
        this.init();
    }

    init() {
        // Wait for Firebase to initialize
        if (!this.auth) {
            setTimeout(() => this.init(), 100);
            return;
        }

        // Listen for authentication state changes
        onAuthStateChanged(this.auth, (user) => {
            if (user) {
                this.currentUser = user;
                this.setupUserInterface();
                this.setupEventListeners();
            } else {
                this.redirectToAuth();
            }
        });
    }

    setupUserInterface() {
        // Display user name
        const userNameElement = document.getElementById('userName');
        if (userNameElement && this.currentUser) {
            // Use displayName if available, otherwise use email
            const displayName = this.currentUser.displayName || this.currentUser.email.split('@')[0];
            userNameElement.textContent = displayName;
        }
    }

    setupEventListeners() {
        // Logout button
        const logoutBtn = document.getElementById('logoutBtn');
        if (logoutBtn) {
            logoutBtn.addEventListener('click', () => {
                this.handleLogout();
            });
        }
    }

    async handleLogout() {
        try {
            // Show logout message with animation
            const logoutBtn = document.getElementById('logoutBtn');
            logoutBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>LOGGING OUT...';
            logoutBtn.disabled = true;

            // Sign out from Firebase
            await signOut(this.auth);

            // Firebase auth state change will handle redirect
        } catch (error) {
            console.error('Logout error:', error);

            // Reset button on error
            const logoutBtn = document.getElementById('logoutBtn');
            logoutBtn.innerHTML = '<i class="fas fa-sign-out-alt mr-2"></i>LOGOUT';
            logoutBtn.disabled = false;

            // Show error message
            this.showErrorMessage('Logout failed. Please try again.');
        }
    }

    showErrorMessage(message) {
        // Create error message element
        const errorDiv = document.createElement('div');
        errorDiv.className = 'fixed top-4 right-4 bg-red-400 text-white p-4 border-4 border-black shadow-[4px_4px_0_#000] z-50 max-w-sm';
        errorDiv.innerHTML = `
            <div class="font-bold text-lg mb-2 uppercase tracking-wide">
                <i class="fas fa-exclamation-triangle mr-2"></i>ERROR
            </div>
            <p class="font-mono text-sm">${message}</p>
        `;

        document.body.appendChild(errorDiv);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (document.body.contains(errorDiv)) {
                document.body.removeChild(errorDiv);
            }
        }, 5000);
    }

    redirectToAuth() {
        window.location.href = 'index.html';
    }
}

// Initialize the main page manager when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new MainPageManager();
});

// Add session timeout functionality (optional)
class SessionManager {
    constructor() {
        this.timeoutDuration = 30 * 60 * 1000; // 30 minutes
        this.warningDuration = 5 * 60 * 1000; // 5 minutes before timeout
        this.init();
    }

    init() {
        this.resetTimeout();
        this.setupActivityListeners();
    }

    setupActivityListeners() {
        // Reset timeout on user activity
        const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];
        
        events.forEach(event => {
            document.addEventListener(event, () => {
                this.resetTimeout();
            }, true);
        });
    }

    resetTimeout() {
        // Clear existing timeouts
        if (this.warningTimeout) clearTimeout(this.warningTimeout);
        if (this.logoutTimeout) clearTimeout(this.logoutTimeout);

        // Set warning timeout
        this.warningTimeout = setTimeout(() => {
            this.showSessionWarning();
        }, this.timeoutDuration - this.warningDuration);

        // Set logout timeout
        this.logoutTimeout = setTimeout(() => {
            this.forceLogout();
        }, this.timeoutDuration);
    }

    showSessionWarning() {
        const warning = document.createElement('div');
        warning.id = 'sessionWarning';
        warning.className = 'fixed top-4 right-4 bg-yellow-400 text-black p-4 border-4 border-black shadow-[4px_4px_0_#000] z-50 max-w-sm';
        warning.innerHTML = `
            <div class="font-bold text-lg mb-2 uppercase tracking-wide">
                <i class="fas fa-exclamation-triangle mr-2"></i>SESSION WARNING
            </div>
            <p class="font-mono text-sm mb-3">Your session will expire in 5 minutes due to inactivity.</p>
            <button id="extendSession" class="w-full py-2 px-4 bg-black text-white font-bold border-2 border-black hover:bg-gray-800 transition-colors uppercase">
                EXTEND SESSION
            </button>
        `;

        document.body.appendChild(warning);

        // Handle extend session button
        document.getElementById('extendSession').addEventListener('click', () => {
            this.resetTimeout();
            document.body.removeChild(warning);
        });

        // Auto-remove warning after 5 minutes
        setTimeout(() => {
            if (document.getElementById('sessionWarning')) {
                document.body.removeChild(warning);
            }
        }, this.warningDuration);
    }

    forceLogout() {
        // Clear user session
        localStorage.removeItem('neoBrutalistCurrentUser');
        
        // Show logout message
        const logoutMessage = document.createElement('div');
        logoutMessage.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
        logoutMessage.innerHTML = `
            <div class="bg-white p-8 border-4 border-black shadow-[8px_8px_0_#000] max-w-md text-center">
                <div class="text-4xl mb-4">
                    <i class="fas fa-clock text-red-500"></i>
                </div>
                <h2 class="text-2xl font-bold mb-4 uppercase tracking-wide">SESSION EXPIRED</h2>
                <p class="font-mono text-gray-700 mb-6">You have been logged out due to inactivity.</p>
                <button id="returnToLogin" class="py-3 px-6 bg-black text-white font-bold text-lg border-4 border-black hover:bg-gray-800 transition-colors uppercase tracking-wide">
                    RETURN TO LOGIN
                </button>
            </div>
        `;

        document.body.appendChild(logoutMessage);

        // Handle return to login button
        document.getElementById('returnToLogin').addEventListener('click', () => {
            window.location.href = 'index.html';
        });

        // Auto-redirect after 10 seconds
        setTimeout(() => {
            window.location.href = 'index.html';
        }, 10000);
    }
}

// Initialize session manager (uncomment to enable)
// new SessionManager();
