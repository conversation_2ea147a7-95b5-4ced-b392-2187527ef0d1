// Enhanced Security System for Wolf CTF Community
// Protects against common client-side attacks and unauthorized access

class SecurityManager {
    constructor() {
        this.isDevToolsOpen = false;
        this.securityViolations = 0;
        this.maxViolations = 5;
        this._isLogging = false;
        this.init();
    }

    init() {
        this.setupErrorHandling();
        this.disableRightClick();
        this.disableKeyboardShortcuts();
        this.disableTextSelection();
        this.disableDragAndDrop();
        this.detectDevTools();
        this.disableConsoleAccess();
        this.preventImageSaving();
        this.addSecurityHeaders();
        this.monitorSecurityViolations();
        this.addVisualSecurityIndicators();
    }

    // Setup global error handling
    setupErrorHandling() {
        // Handle uncaught JavaScript errors
        window.addEventListener('error', (event) => {
            // Suppress external script errors (like Google API)
            if (event.filename && (
                event.filename.includes('gapi.') ||
                event.filename.includes('api.js') ||
                event.filename.includes('google') ||
                event.filename.includes('gstatic.com')
            )) {
                event.preventDefault();
                return true; // Suppress the error
            }

            // Log other errors as potential security issues
            if (event.error && event.error.stack) {
                console.log('🛡️ Security: JavaScript error detected:', event.error.message);
            }
        });

        // Handle unhandled promise rejections
        window.addEventListener('unhandledrejection', (event) => {
            // Suppress external API promise rejections
            if (event.reason && event.reason.toString().includes('gapi')) {
                event.preventDefault();
                return true;
            }

            console.log('🛡️ Security: Unhandled promise rejection:', event.reason);
        });
    }

    // Disable right-click context menu
    disableRightClick() {
        document.addEventListener('contextmenu', (e) => {
            e.preventDefault();
            // Silently log without showing popup
            this.logSecurityViolation('Right-click attempt');
            return false;
        });

        // Additional protection for touch devices
        document.addEventListener('touchstart', (e) => {
            if (e.touches.length > 1) {
                e.preventDefault();
            }
        });
    }

    // Disable common keyboard shortcuts
    disableKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Disable F12, Ctrl+Shift+I, Ctrl+Shift+J, Ctrl+U, Ctrl+S, Ctrl+A, Ctrl+P
            if (
                e.key === 'F12' ||
                (e.ctrlKey && e.shiftKey && (e.key === 'I' || e.key === 'J' || e.key === 'C')) ||
                (e.ctrlKey && (e.key === 'u' || e.key === 'U')) ||
                (e.ctrlKey && (e.key === 's' || e.key === 'S')) ||
                (e.ctrlKey && (e.key === 'a' || e.key === 'A')) ||
                (e.ctrlKey && (e.key === 'p' || e.key === 'P')) ||
                (e.ctrlKey && e.shiftKey && (e.key === 'K')) ||
                e.key === 'F5' ||
                (e.ctrlKey && e.key === 'r')
            ) {
                e.preventDefault();
                // Silently log without showing popup
                this.logSecurityViolation(`Keyboard shortcut attempt: ${e.key}`);
                return false;
            }
        });
    }

    // Disable text selection
    disableTextSelection() {
        document.addEventListener('selectstart', (e) => {
            e.preventDefault();
            return false;
        });

        document.addEventListener('mousedown', (e) => {
            if (e.detail > 1) { // Prevent multiple clicks
                e.preventDefault();
            }
        });

        // CSS-based text selection prevention
        const style = document.createElement('style');
        style.textContent = `
            * {
                -webkit-user-select: none !important;
                -moz-user-select: none !important;
                -ms-user-select: none !important;
                user-select: none !important;
                -webkit-touch-callout: none !important;
                -webkit-tap-highlight-color: transparent !important;
            }
            input, textarea {
                -webkit-user-select: text !important;
                -moz-user-select: text !important;
                -ms-user-select: text !important;
                user-select: text !important;
            }
        `;
        document.head.appendChild(style);
    }

    // Disable drag and drop
    disableDragAndDrop() {
        document.addEventListener('dragstart', (e) => {
            e.preventDefault();
            // Silently prevent without showing popup
            return false;
        });

        document.addEventListener('drop', (e) => {
            e.preventDefault();
            return false;
        });

        document.addEventListener('dragover', (e) => {
            e.preventDefault();
            return false;
        });
    }

    // Detect developer tools (disabled for better user experience)
    detectDevTools() {
        // Developer tools detection is disabled to improve user experience
        // Uncomment the code below if you want to re-enable detection

        /*
        let devtools = {
            open: false,
            orientation: null
        };

        const threshold = 160;

        setInterval(() => {
            if (window.outerHeight - window.innerHeight > threshold ||
                window.outerWidth - window.innerWidth > threshold) {
                if (!devtools.open) {
                    devtools.open = true;
                    this.handleDevToolsDetection();
                }
            } else {
                devtools.open = false;
            }
        }, 500);

        // Additional detection method
        let element = new Image();
        Object.defineProperty(element, 'id', {
            get: () => {
                this.handleDevToolsDetection();
                return 'devtools-detector';
            }
        });

        console.log('%c', element);
        */
    }

    // Handle developer tools detection
    handleDevToolsDetection() {
        this.isDevToolsOpen = true;
        // Silently log without showing popup
        this.logSecurityViolation('Developer tools opened. They are not allowed. Same Safty Reasons.');

        // Optional: Redirect or block access (disabled for better UX)
        // if (this.securityViolations > this.maxViolations) {
        //     this.blockAccess('Too many security violations detected');
        // }
    }

    // Disable console access
    disableConsoleAccess() {
        // Store original console for internal use
        const originalConsole = { ...console };

        // Allow security system to use console
        window._securityConsole = originalConsole;

        // Override console methods but allow security logging
        const securityManager = this;

        ['log', 'warn', 'error', 'info', 'debug', 'clear', 'dir', 'dirxml', 'table', 'trace', 'assert', 'count', 'countReset', 'group', 'groupCollapsed', 'groupEnd', 'time', 'timeEnd', 'timeLog', 'profile', 'profileEnd'].forEach(key => {
            console[key] = (...args) => {
                // Allow security-related logs and internal security system logs
                if (args[0] && typeof args[0] === 'string' &&
                    (args[0].includes('🛡️ Security:') ||
                     args[0].includes('Security violation logged:') ||
                     args[0].includes('🔥') ||
                     args[0].includes('✅') ||
                     args[0].includes('❌'))) {
                    originalConsole[key](...args);
                } else {
                    // Silently ignore other console usage to prevent infinite loop
                    // Only log if not already in a logging operation
                    if (!securityManager._isLogging) {
                        securityManager._isLogging = true;
                        try {
                            // Use original console to avoid recursion
                            originalConsole.warn('🛡️ Security: Console access blocked');
                        } finally {
                            securityManager._isLogging = false;
                        }
                    }
                }
            };
        });
    }

    // Prevent image saving
    preventImageSaving() {
        document.addEventListener('DOMContentLoaded', () => {
            const images = document.querySelectorAll('img');
            images.forEach(img => {
                img.addEventListener('dragstart', (e) => {
                    e.preventDefault();
                    return false;
                });
                
                img.style.pointerEvents = 'none';
                img.setAttribute('draggable', 'false');
            });
        });
    }

    // Add security headers (client-side simulation)
    addSecurityHeaders() {
        // Add meta tags for security (only those that work in HTML)
        const securityMetas = [
            { name: 'referrer', content: 'no-referrer' },
            { name: 'robots', content: 'noindex, nofollow, noarchive, nosnippet, noimageindex' }
        ];

        securityMetas.forEach(meta => {
            // Check if meta tag already exists
            const existingMeta = document.querySelector(`meta[name="${meta.name}"]`);
            if (!existingMeta) {
                const metaTag = document.createElement('meta');
                Object.keys(meta).forEach(key => {
                    metaTag.setAttribute(key, meta[key]);
                });
                document.head.appendChild(metaTag);
            }
        });

        // Log that security headers should be set server-side
        console.log('🛡️ Security: Client-side protection active. Server-side headers recommended for production.');
    }

    // Monitor security violations
    monitorSecurityViolations() {
        // Monitor for suspicious activity
        let clickCount = 0;
        let keyCount = 0;
        
        document.addEventListener('click', () => {
            clickCount++;
            if (clickCount > 100) { // Suspicious rapid clicking
                this.logSecurityViolation('Suspicious clicking behavior');
                clickCount = 0;
            }
        });

        document.addEventListener('keydown', () => {
            keyCount++;
            if (keyCount > 200) { // Suspicious rapid key pressing
                this.logSecurityViolation('Suspicious keyboard behavior');
                keyCount = 0;
            }
        });

        // Reset counters periodically
        setInterval(() => {
            clickCount = 0;
            keyCount = 0;
        }, 60000); // Reset every minute
    }

    // Add visual security indicators
    addVisualSecurityIndicators() {
        // Add security badge
        const securityBadge = document.createElement('div');
        securityBadge.id = 'security-badge';
        securityBadge.innerHTML = `
            <div style="position: fixed; top: 10px; left: 10px; background: #000; color: #00ff00; padding: 5px 10px; font-family: monospace; font-size: 12px; z-index: 9999; border: 2px solid #00ff00; box-shadow: 0 0 10px rgba(0,255,0,0.3);">
                <i class="fas fa-shield-alt"></i> SECURED
            </div>
        `;
        document.body.appendChild(securityBadge);

        // Add watermark
        this.addWatermark();
    }

    // Add watermark
    addWatermark() {
        const watermark = document.createElement('div');
        watermark.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) rotate(-45deg);
            font-size: 48px;
            color: rgba(0, 0, 0, 0.05);
            font-weight: bold;
            pointer-events: none;
            z-index: 1000;
            font-family: 'Bebas Neue', sans-serif;
            letter-spacing: 10px;
        `;
        watermark.textContent = 'WOLF CTF COMMUNITY';
        document.body.appendChild(watermark);
    }

    // Show security alert
    showSecurityAlert(message) {
        const alert = document.createElement('div');
        alert.className = 'security-alert';
        alert.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #ff4444;
            color: white;
            padding: 15px 20px;
            border: 4px solid #000;
            box-shadow: 4px 4px 0 #000;
            font-family: 'Roboto Mono', monospace;
            font-weight: bold;
            z-index: 10000;
            max-width: 300px;
            animation: slideIn 0.3s ease-out;
        `;
        alert.innerHTML = `
            <div style="display: flex; align-items: center; gap: 10px;">
                <i class="fas fa-exclamation-triangle"></i>
                <span>${message}</span>
            </div>
        `;

        document.body.appendChild(alert);

        setTimeout(() => {
            if (document.body.contains(alert)) {
                document.body.removeChild(alert);
            }
        }, 3000);
    }

    // Show security warning (disabled for better UX)
    showSecurityWarning(message) {
        // Security warnings are disabled for better user experience
        // Only log to console instead of showing intrusive popups
        console.log('🛡️ Security: ' + message);
    }

    // Log security violation
    logSecurityViolation(violation) {
        // Prevent infinite recursion
        if (this._isLogging) return;

        this._isLogging = true;
        try {
            this.securityViolations++;
            const timestamp = new Date().toISOString();
            const logEntry = {
                timestamp,
                violation,
                userAgent: navigator.userAgent,
                url: window.location.href,
                violationCount: this.securityViolations
            };

            // Store in localStorage (in production, send to server)
            const logs = JSON.parse(localStorage.getItem('securityLogs') || '[]');
            logs.push(logEntry);
            localStorage.setItem('securityLogs', JSON.stringify(logs.slice(-100))); // Keep last 100 logs

            // Use original console to avoid recursion
            if (window._securityConsole) {
                window._securityConsole.warn('🛡️ Security: Security violation logged:', logEntry);
            }

            if (this.securityViolations >= this.maxViolations) {
                this.blockAccess('Maximum security violations exceeded');
            }
        } finally {
            this._isLogging = false;
        }
    }

    // Block access
    blockAccess(reason) {
        document.body.innerHTML = `
            <div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: #000; color: #ff4444; display: flex; align-items: center; justify-content: center; font-family: 'Roboto Mono', monospace; z-index: 999999;">
                <div style="text-align: center; border: 4px solid #ff4444; padding: 40px; background: #111;">
                    <i class="fas fa-ban" style="font-size: 64px; margin-bottom: 20px;"></i>
                    <h1 style="font-size: 32px; margin-bottom: 20px;">ACCESS BLOCKED</h1>
                    <p style="font-size: 18px; margin-bottom: 20px;">${reason}</p>
                    <p style="font-size: 14px; color: #ccc;">Contact administrator if you believe this is an error.</p>
                </div>
            </div>
        `;

        // Disable all interactions
        document.addEventListener('click', (e) => e.preventDefault(), true);
        document.addEventListener('keydown', (e) => e.preventDefault(), true);
        
        // Optional: Redirect after delay
        setTimeout(() => {
            window.location.href = 'about:blank';
        }, 10000);
    }

    // Get security logs (for admin purposes)
    getSecurityLogs() {
        return JSON.parse(localStorage.getItem('securityLogs') || '[]');
    }

    // Clear security logs
    clearSecurityLogs() {
        localStorage.removeItem('securityLogs');
        this.securityViolations = 0;
    }
}

// Initialize security manager
document.addEventListener('DOMContentLoaded', () => {
    window.securityManager = new SecurityManager();
});

// Add CSS animations
const securityStyles = document.createElement('style');
securityStyles.textContent = `
    @keyframes slideIn {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
    
    .security-alert {
        animation: slideIn 0.3s ease-out;
    }
    
    /* Disable image context menu */
    img {
        -webkit-user-drag: none;
        -khtml-user-drag: none;
        -moz-user-drag: none;
        -o-user-drag: none;
        user-drag: none;
        pointer-events: none;
    }
    
    /* Disable highlighting */
    ::selection {
        background: transparent;
    }
    
    ::-moz-selection {
        background: transparent;
    }
`;
document.head.appendChild(securityStyles);
