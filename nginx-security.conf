# Wolf CTF Community - Nginx Security Configuration
# Include this file in your nginx server block

# Security Headers
add_header X-Content-Type-Options "nosniff" always;
add_header X-Frame-Options "DENY" always;
add_header X-XSS-Protection "1; mode=block" always;
add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
add_header Referrer-Policy "no-referrer" always;
add_header Permissions-Policy "camera=(), microphone=(), geolocation=(), payment=(), usb=(), magnetometer=(), gyroscope=(), accelerometer=()" always;

# Content Security Policy
add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.tailwindcss.com https://www.gstatic.com https://cdnjs.cloudflare.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdnjs.cloudflare.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data:; connect-src 'self' https://*.firebaseapp.com https://*.googleapis.com; frame-ancestors 'none'; base-uri 'self'; form-action 'self'" always;

# Hide server information
server_tokens off;
more_clear_headers Server;
more_clear_headers X-Powered-By;

# Rate limiting
limit_req_zone $binary_remote_addr zone=login:10m rate=5r/m;
limit_req_zone $binary_remote_addr zone=general:10m rate=10r/s;

# Apply rate limiting
location /auth {
    limit_req zone=login burst=3 nodelay;
}

location / {
    limit_req zone=general burst=20 nodelay;
}

# Block suspicious requests
location ~* \.(php|asp|aspx|jsp)$ {
    return 444;
}

# Block access to sensitive files
location ~* \.(htaccess|htpasswd|ini|log|sh|inc|bak|config|sql|git)$ {
    deny all;
    return 404;
}

# Block bad user agents
if ($http_user_agent ~* (libwww-perl|python|nikto|scan|java|winhttp|clshttp|loader|curl|wget)) {
    return 444;
}

# Block empty user agents
if ($http_user_agent = "") {
    return 444;
}

# Prevent hotlinking
location ~* \.(jpg|jpeg|png|gif|css|js)$ {
    valid_referers none blocked server_names yourdomain.com *.yourdomain.com;
    if ($invalid_referer) {
        return 403;
    }
}

# Gzip compression
gzip on;
gzip_vary on;
gzip_min_length 1024;
gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

# Cache static files
location ~* \.(jpg|jpeg|png|gif|ico|css|js|woff|woff2|ttf|eot)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}

# Disable caching for HTML files
location ~* \.(html|htm)$ {
    expires -1;
    add_header Cache-Control "no-cache, no-store, must-revalidate";
    add_header Pragma "no-cache";
}

# SSL Configuration (if using HTTPS)
ssl_protocols TLSv1.2 TLSv1.3;
ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
ssl_prefer_server_ciphers off;
ssl_session_cache shared:SSL:10m;
ssl_session_timeout 10m;

# OCSP Stapling
ssl_stapling on;
ssl_stapling_verify on;

# Client body size limit
client_max_body_size 10M;

# Timeout settings
client_body_timeout 12;
client_header_timeout 12;
keepalive_timeout 15;
send_timeout 10;

# Buffer size settings
client_body_buffer_size 10K;
client_header_buffer_size 1k;
large_client_header_buffers 2 1k;
