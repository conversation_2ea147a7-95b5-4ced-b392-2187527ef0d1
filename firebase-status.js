// Firebase Connection Status Indicator
// Shows real-time Firebase connection status to users

class FirebaseStatus {
    constructor() {
        this.isConnected = false;
        this.connectionAttempts = 0;
        this.maxAttempts = 3;
        this.statusIndicator = null;
        this.init();
    }

    init() {
        this.createStatusIndicator();
        this.setupEventListeners();
        this.checkInitialConnection();
    }

    createStatusIndicator() {
        this.statusIndicator = document.createElement('div');
        this.statusIndicator.id = 'firebase-status';
        this.statusIndicator.style.cssText = `
            position: fixed;
            top: 10px;
            left: 10px;
            background: #333;
            color: white;
            padding: 8px 12px;
            font-family: 'Roboto Mono', monospace;
            font-size: 12px;
            border: 2px solid #666;
            border-radius: 4px;
            z-index: 9999;
            transition: all 0.3s ease;
            cursor: pointer;
            user-select: none;
        `;
        
        this.updateStatus('connecting', 'Connecting to Firebase...');
        document.body.appendChild(this.statusIndicator);

        // Auto-hide after successful connection
        this.statusIndicator.addEventListener('click', () => {
            if (this.isConnected) {
                this.hideStatus();
            } else {
                this.showConnectionHelp();
            }
        });
    }

    setupEventListeners() {
        // Listen for Firebase events
        window.addEventListener('firebaseReady', () => {
            this.handleConnectionSuccess();
        });

        window.addEventListener('firebaseError', (event) => {
            this.handleConnectionError(event.detail.error);
        });

        // Monitor network status
        window.addEventListener('online', () => {
            this.updateStatus('connecting', 'Network restored, reconnecting...');
            this.attemptReconnection();
        });

        window.addEventListener('offline', () => {
            this.updateStatus('offline', 'Network offline');
        });
    }

    checkInitialConnection() {
        // Check if Firebase is already connected
        if (window.firebaseAuth) {
            this.handleConnectionSuccess();
        } else {
            // Wait a bit for Firebase to initialize
            setTimeout(() => {
                if (!window.firebaseAuth) {
                    this.connectionAttempts++;
                    if (this.connectionAttempts < this.maxAttempts) {
                        this.checkInitialConnection();
                    } else {
                        this.handleConnectionError(new Error('Firebase initialization timeout'));
                    }
                }
            }, 1000);
        }
    }

    handleConnectionSuccess() {
        this.isConnected = true;
        this.connectionAttempts = 0;
        this.updateStatus('connected', 'Firebase Connected');
        
        // Auto-hide after 3 seconds
        setTimeout(() => {
            if (this.isConnected) {
                this.hideStatus();
            }
        }, 3000);
    }

    handleConnectionError(error) {
        this.isConnected = false;
        console.error('Firebase connection error:', error);

        let errorMessage = 'Firebase Connection Failed';
        let statusType = 'error';
        let showHelp = false;

        if (error.code) {
            switch (error.code) {
                case 'auth/network-request-failed':
                    errorMessage = 'Network Error - Check Connection';
                    statusType = 'network-error';
                    showHelp = true;
                    break;
                case 'auth/api-key-not-valid':
                    errorMessage = 'Invalid API Configuration';
                    break;
                case 'auth/too-many-requests':
                    errorMessage = 'Too Many Requests - Wait';
                    break;
                case 'auth/app-not-authorized':
                    errorMessage = 'App Not Authorized - Check Domain';
                    break;
                default:
                    errorMessage = `Firebase Error: ${error.code}`;
            }
        } else if (error.message) {
            if (error.message.includes('timeout') || error.message.includes('Timeout')) {
                errorMessage = 'Connection Timeout - Slow Network';
                statusType = 'timeout';
                showHelp = true;
            } else if (error.message.includes('network') || error.message.includes('Failed to fetch')) {
                errorMessage = 'Network Error - Check Internet';
                statusType = 'network-error';
                showHelp = true;
            } else if (error.message.includes('DNS') || error.message.includes('ERR_NAME_NOT_RESOLVED')) {
                errorMessage = 'DNS Error - Check DNS Settings';
                statusType = 'network-error';
                showHelp = true;
            } else if (error.message.includes('CORS')) {
                errorMessage = 'CORS Error - Domain Issue';
                statusType = 'error';
            } else if (error.message.includes('Firebase SDK load timeout')) {
                errorMessage = 'Firebase SDK Load Failed';
                statusType = 'timeout';
                showHelp = true;
            }
        }

        this.updateStatus(statusType, errorMessage);

        // Auto-show help for network issues
        if (showHelp) {
            setTimeout(() => this.showConnectionHelp(), 2000);
        }
    }

    updateStatus(type, message) {
        if (!this.statusIndicator) return;

        let backgroundColor, borderColor, textColor;
        
        switch (type) {
            case 'connecting':
                backgroundColor = '#ffa500';
                borderColor = '#ff8c00';
                textColor = '#000';
                break;
            case 'connected':
                backgroundColor = '#00aa00';
                borderColor = '#008800';
                textColor = '#fff';
                break;
            case 'error':
                backgroundColor = '#ff4444';
                borderColor = '#cc0000';
                textColor = '#fff';
                break;
            case 'network-error':
                backgroundColor = '#ff6600';
                borderColor = '#cc4400';
                textColor = '#fff';
                break;
            case 'timeout':
                backgroundColor = '#ffaa00';
                borderColor = '#cc8800';
                textColor = '#000';
                break;
            case 'offline':
                backgroundColor = '#666';
                borderColor = '#444';
                textColor = '#fff';
                break;
            default:
                backgroundColor = '#333';
                borderColor = '#666';
                textColor = '#fff';
        }

        this.statusIndicator.style.backgroundColor = backgroundColor;
        this.statusIndicator.style.borderColor = borderColor;
        this.statusIndicator.style.color = textColor;
        
        // Add icon based on status
        let icon = '';
        switch (type) {
            case 'connecting':
                icon = '🔄';
                break;
            case 'connected':
                icon = '✅';
                break;
            case 'error':
            case 'network-error':
            case 'timeout':
                icon = '❌';
                break;
            case 'offline':
                icon = '📡';
                break;
        }
        
        this.statusIndicator.innerHTML = `${icon} ${message}`;
        
        // Add pulse animation for connecting state
        if (type === 'connecting') {
            this.statusIndicator.style.animation = 'pulse 1s infinite';
        } else {
            this.statusIndicator.style.animation = 'none';
        }
    }

    hideStatus() {
        if (this.statusIndicator) {
            this.statusIndicator.style.opacity = '0';
            this.statusIndicator.style.transform = 'translateX(-100%)';
            
            setTimeout(() => {
                if (this.statusIndicator && document.body.contains(this.statusIndicator)) {
                    document.body.removeChild(this.statusIndicator);
                }
            }, 300);
        }
    }

    showConnectionHelp() {
        const helpModal = document.createElement('div');
        helpModal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
            font-family: 'Roboto Mono', monospace;
        `;

        helpModal.innerHTML = `
            <div style="background: white; padding: 30px; border: 4px solid #000; box-shadow: 8px 8px 0 #000; max-width: 500px; text-align: center;">
                <h2 style="font-size: 24px; margin-bottom: 20px; color: #000;">🔥 Firebase Connection Help</h2>
                <div style="text-align: left; margin-bottom: 20px; color: #333;">
                    <p><strong>Connection Issues:</strong></p>
                    <ul style="margin: 10px 0; padding-left: 20px;">
                        <li>Check your internet connection</li>
                        <li>Refresh the page to retry</li>
                        <li>Clear browser cache if problems persist</li>
                        <li>Disable ad blockers that might block Firebase</li>
                    </ul>
                    <p><strong>Current Status:</strong> ${this.statusIndicator.textContent}</p>
                </div>
                <div style="display: flex; gap: 10px; justify-content: center;">
                    <button id="retryConnection" style="padding: 10px 20px; background: #00aa00; color: white; border: 2px solid #000; font-weight: bold; cursor: pointer;">
                        🔄 RETRY
                    </button>
                    <button id="closeHelp" style="padding: 10px 20px; background: #666; color: white; border: 2px solid #000; font-weight: bold; cursor: pointer;">
                        CLOSE
                    </button>
                </div>
            </div>
        `;

        document.body.appendChild(helpModal);

        // Handle buttons
        document.getElementById('retryConnection').addEventListener('click', () => {
            document.body.removeChild(helpModal);
            this.attemptReconnection();
        });

        document.getElementById('closeHelp').addEventListener('click', () => {
            document.body.removeChild(helpModal);
        });

        // Close on background click
        helpModal.addEventListener('click', (e) => {
            if (e.target === helpModal) {
                document.body.removeChild(helpModal);
            }
        });
    }

    attemptReconnection() {
        this.updateStatus('connecting', 'Attempting to reconnect...');
        
        // Force page reload to reinitialize Firebase
        setTimeout(() => {
            window.location.reload();
        }, 1000);
    }
}

// Initialize Firebase status indicator
document.addEventListener('DOMContentLoaded', () => {
    window.firebaseStatus = new FirebaseStatus();
});

// Add CSS for pulse animation
const statusStyles = document.createElement('style');
statusStyles.textContent = `
    @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.7; }
    }
`;
document.head.appendChild(statusStyles);
