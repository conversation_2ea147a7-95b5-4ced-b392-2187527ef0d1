// Advanced Security Monitoring System for Wolf CTF Community
// Real-time threat detection and response

class SecurityMonitor {
    constructor() {
        this.threats = [];
        this.blockedIPs = new Set();
        this.suspiciousActivity = new Map();
        this.securityLevel = 'HIGH';
        this.init();
    }

    init() {
        this.startRealTimeMonitoring();
        this.setupThreatDetection();
        this.initializeSecurityDashboard();
        this.startPeriodicScans();
    }

    // Real-time monitoring
    startRealTimeMonitoring() {
        // Monitor mouse movements for bot detection
        let mouseMovements = 0;
        let lastMouseTime = Date.now();
        
        document.addEventListener('mousemove', () => {
            mouseMovements++;
            const now = Date.now();
            const timeDiff = now - lastMouseTime;
            
            // Detect unnatural mouse movements (bots)
            if (timeDiff < 10 && mouseMovements > 100) {
                this.reportThreat('Suspicious mouse activity detected', 'BOT_DETECTION');
            }
            
            lastMouseTime = now;
        });

        // Monitor keyboard patterns
        let keystrokes = [];
        document.addEventListener('keydown', (e) => {
            keystrokes.push({
                key: e.key,
                timestamp: Date.now(),
                ctrlKey: e.ctrl<PERSON>ey,
                altKey: e.altKey,
                shiftKey: e.shiftKey
            });

            // Keep only last 50 keystrokes
            if (keystrokes.length > 50) {
                keystrokes = keystrokes.slice(-50);
            }

            // Detect rapid automated keystrokes
            if (keystrokes.length >= 10) {
                const recentKeys = keystrokes.slice(-10);
                const timeSpan = recentKeys[9].timestamp - recentKeys[0].timestamp;
                if (timeSpan < 500) { // 10 keys in less than 500ms
                    this.reportThreat('Automated keystroke pattern detected', 'BOT_DETECTION');
                }
            }
        });

        // Monitor network requests
        this.interceptNetworkRequests();
    }

    // Intercept and monitor network requests (now handled by fetch manager)
    interceptNetworkRequests() {
        // Network request monitoring is now managed by the centralized fetch manager
        // This method is kept for compatibility but functionality moved to fetch-manager.js
        console.log('🛡️ Network request monitoring delegated to fetch manager');

        const originalXHR = window.XMLHttpRequest;

        // Monitor XMLHttpRequest
        const monitor = this;
        window.XMLHttpRequest = function() {
            const xhr = new originalXHR();
            const originalOpen = xhr.open;
            
            xhr.open = function(method, url, ...args) {
                monitor.logNetworkRequest('XHR', url);
                
                if (monitor.isSuspiciousURL(url)) {
                    monitor.reportThreat(`Suspicious XHR request to: ${url}`, 'NETWORK_THREAT');
                    throw new Error('Request blocked by security system');
                }
                
                return originalOpen.apply(this, [method, url, ...args]);
            };
            
            return xhr;
        };
    }

    // Check if URL is suspicious
    isSuspiciousURL(url) {
        const suspiciousPatterns = [
            /malware/i,
            /phishing/i,
            /exploit/i,
            /hack/i,
            /\.onion/,
            /bit\.ly/,
            /tinyurl/,
            /suspicious-domain\.com/
        ];

        return suspiciousPatterns.some(pattern => pattern.test(url));
    }

    // Log network requests
    logNetworkRequest(type, url) {
        const log = {
            type,
            url,
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent,
            referrer: document.referrer
        };

        // Store in security logs
        const logs = JSON.parse(localStorage.getItem('networkLogs') || '[]');
        logs.push(log);
        localStorage.setItem('networkLogs', JSON.stringify(logs.slice(-1000))); // Keep last 1000 logs
    }

    // Setup threat detection
    setupThreatDetection() {
        // Detect multiple failed login attempts
        this.monitorLoginAttempts();
        
        // Detect SQL injection attempts
        this.monitorSQLInjection();
        
        // Detect XSS attempts
        this.monitorXSSAttempts();
        
        // Monitor for suspicious form submissions
        this.monitorFormSubmissions();
    }

    // Monitor login attempts
    monitorLoginAttempts() {
        const loginForms = document.querySelectorAll('form');
        loginForms.forEach(form => {
            form.addEventListener('submit', () => {
                const formData = new FormData(form);
                const email = formData.get('email') || formData.get('username');
                
                if (email) {
                    this.trackLoginAttempt(email);
                }
            });
        });
    }

    // Track login attempts per email
    trackLoginAttempt(email) {
        const attempts = this.suspiciousActivity.get(email) || { count: 0, lastAttempt: 0 };
        const now = Date.now();
        
        // Reset counter if last attempt was more than 15 minutes ago
        if (now - attempts.lastAttempt > 15 * 60 * 1000) {
            attempts.count = 0;
        }
        
        attempts.count++;
        attempts.lastAttempt = now;
        
        this.suspiciousActivity.set(email, attempts);
        
        // Report if too many attempts
        if (attempts.count > 5) {
            this.reportThreat(`Multiple login attempts for email: ${email}`, 'BRUTE_FORCE');
        }
    }

    // Monitor for SQL injection
    monitorSQLInjection() {
        const sqlPatterns = [
            /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)/i,
            /(\b(OR|AND)\s+\d+\s*=\s*\d+)/i,
            /(\'|\"|;|--|\*|\|)/,
            /(\bSCRIPT\b)/i
        ];

        document.addEventListener('input', (e) => {
            const value = e.target.value;
            if (sqlPatterns.some(pattern => pattern.test(value))) {
                this.reportThreat(`SQL injection attempt detected in input: ${e.target.name}`, 'SQL_INJECTION');
                e.target.value = '';
                e.preventDefault();
            }
        });
    }

    // Monitor for XSS attempts
    monitorXSSAttempts() {
        const xssPatterns = [
            /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
            /javascript:/i,
            /on\w+\s*=/i,
            /<iframe/i,
            /<object/i,
            /<embed/i
        ];

        document.addEventListener('input', (e) => {
            const value = e.target.value;
            if (xssPatterns.some(pattern => pattern.test(value))) {
                this.reportThreat(`XSS attempt detected in input: ${e.target.name}`, 'XSS_ATTEMPT');
                e.target.value = '';
                e.preventDefault();
            }
        });
    }

    // Monitor form submissions
    monitorFormSubmissions() {
        document.addEventListener('submit', (e) => {
            const form = e.target;
            const formData = new FormData(form);
            
            // Check for suspicious form data
            for (let [key, value] of formData.entries()) {
                if (typeof value === 'string' && value.length > 10000) {
                    this.reportThreat(`Unusually large form data in field: ${key}`, 'FORM_ABUSE');
                    e.preventDefault();
                    return;
                }
            }
        });
    }

    // Report security threat
    reportThreat(description, type) {
        const threat = {
            id: Date.now(),
            description,
            type,
            timestamp: new Date().toISOString(),
            url: window.location.href,
            userAgent: navigator.userAgent,
            ip: 'client-side', // Would be populated server-side
            severity: this.getThreatSeverity(type)
        };

        this.threats.push(threat);
        this.logThreat(threat);
        this.handleThreat(threat);
        this.updateSecurityDashboard();
    }

    // Get threat severity
    getThreatSeverity(type) {
        const severityMap = {
            'BOT_DETECTION': 'MEDIUM',
            'NETWORK_THREAT': 'HIGH',
            'BRUTE_FORCE': 'HIGH',
            'SQL_INJECTION': 'CRITICAL',
            'XSS_ATTEMPT': 'HIGH',
            'FORM_ABUSE': 'MEDIUM'
        };
        return severityMap[type] || 'LOW';
    }

    // Handle threat based on severity
    handleThreat(threat) {
        switch (threat.severity) {
            case 'CRITICAL':
                this.blockUser();
                this.showCriticalAlert(threat);
                break;
            case 'HIGH':
                this.showHighAlert(threat);
                break;
            case 'MEDIUM':
                this.showMediumAlert(threat);
                break;
        }
    }

    // Block user
    blockUser() {
        document.body.innerHTML = `
            <div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: #000; color: #ff0000; display: flex; align-items: center; justify-content: center; font-family: 'Roboto Mono', monospace; z-index: 999999;">
                <div style="text-align: center; border: 4px solid #ff0000; padding: 40px; background: #111; animation: pulse 1s infinite;">
                    <i class="fas fa-skull-crossbones" style="font-size: 64px; margin-bottom: 20px;"></i>
                    <h1 style="font-size: 32px; margin-bottom: 20px;">SECURITY BREACH DETECTED</h1>
                    <p style="font-size: 18px; margin-bottom: 20px;">Critical security violation detected.</p>
                    <p style="font-size: 14px; color: #ccc;">Access has been terminated and logged.</p>
                    <div style="margin-top: 30px; font-size: 12px; color: #666;">
                        Incident ID: ${Date.now()}<br>
                        Time: ${new Date().toISOString()}
                    </div>
                </div>
            </div>
        `;
    }

    // Show critical alert
    showCriticalAlert(threat) {
        const alert = document.createElement('div');
        alert.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #ff0000;
            color: white;
            padding: 20px;
            border: 4px solid #000;
            box-shadow: 4px 4px 0 #000;
            font-family: 'Roboto Mono', monospace;
            font-weight: bold;
            z-index: 10000;
            max-width: 400px;
            animation: pulse 1s infinite;
        `;
        alert.innerHTML = `
            <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 10px;">
                <i class="fas fa-exclamation-triangle" style="font-size: 24px;"></i>
                <span style="font-size: 18px;">CRITICAL THREAT</span>
            </div>
            <div style="font-size: 14px; margin-bottom: 10px;">${threat.description}</div>
            <div style="font-size: 12px; color: #ffcccc;">Type: ${threat.type}</div>
        `;

        document.body.appendChild(alert);
    }

    // Show high alert
    showHighAlert(threat) {
        const alert = document.createElement('div');
        alert.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #ff6600;
            color: white;
            padding: 15px;
            border: 4px solid #000;
            box-shadow: 4px 4px 0 #000;
            font-family: 'Roboto Mono', monospace;
            font-weight: bold;
            z-index: 10000;
            max-width: 350px;
        `;
        alert.innerHTML = `
            <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 10px;">
                <i class="fas fa-shield-alt" style="font-size: 20px;"></i>
                <span style="font-size: 16px;">HIGH THREAT</span>
            </div>
            <div style="font-size: 12px;">${threat.description}</div>
        `;

        document.body.appendChild(alert);

        setTimeout(() => {
            if (document.body.contains(alert)) {
                document.body.removeChild(alert);
            }
        }, 5000);
    }

    // Show medium alert
    showMediumAlert(threat) {
        console.warn('Security Alert:', threat);
    }

    // Log threat
    logThreat(threat) {
        const logs = JSON.parse(localStorage.getItem('securityThreats') || '[]');
        logs.push(threat);
        localStorage.setItem('securityThreats', JSON.stringify(logs.slice(-500))); // Keep last 500 threats
    }

    // Initialize security dashboard
    initializeSecurityDashboard() {
        // Create floating security status indicator
        const indicator = document.createElement('div');
        indicator.id = 'security-status';
        indicator.style.cssText = `
            position: fixed;
            bottom: 20px;
            left: 20px;
            background: #000;
            color: #00ff00;
            padding: 10px 15px;
            font-family: 'Roboto Mono', monospace;
            font-size: 12px;
            border: 2px solid #00ff00;
            box-shadow: 0 0 10px rgba(0,255,0,0.3);
            z-index: 9998;
            cursor: pointer;
            transition: all 0.3s ease;
        `;
        indicator.innerHTML = `
            <div style="display: flex; align-items: center; gap: 8px;">
                <i class="fas fa-shield-alt"></i>
                <span>SECURITY: ${this.securityLevel}</span>
                <span id="threat-count">0</span>
            </div>
        `;

        indicator.addEventListener('click', () => {
            this.showSecurityDashboard();
        });

        document.body.appendChild(indicator);
    }

    // Update security dashboard
    updateSecurityDashboard() {
        const threatCount = document.getElementById('threat-count');
        if (threatCount) {
            threatCount.textContent = this.threats.length;
        }

        // Update security level based on recent threats
        const recentThreats = this.threats.filter(t => 
            Date.now() - new Date(t.timestamp).getTime() < 5 * 60 * 1000 // Last 5 minutes
        );

        if (recentThreats.some(t => t.severity === 'CRITICAL')) {
            this.securityLevel = 'CRITICAL';
        } else if (recentThreats.some(t => t.severity === 'HIGH')) {
            this.securityLevel = 'HIGH';
        } else if (recentThreats.length > 0) {
            this.securityLevel = 'MEDIUM';
        } else {
            this.securityLevel = 'NORMAL';
        }

        const statusElement = document.querySelector('#security-status span');
        if (statusElement) {
            statusElement.textContent = `SECURITY: ${this.securityLevel}`;
        }
    }

    // Show security dashboard
    showSecurityDashboard() {
        const dashboard = document.createElement('div');
        dashboard.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #000;
            color: #00ff00;
            padding: 30px;
            border: 4px solid #00ff00;
            box-shadow: 0 0 20px rgba(0,255,0,0.5);
            font-family: 'Roboto Mono', monospace;
            z-index: 99999;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        `;

        const recentThreats = this.threats.slice(-10).reverse();
        
        dashboard.innerHTML = `
            <div style="display: flex; justify-content: between; align-items: center; margin-bottom: 20px;">
                <h2 style="font-size: 24px; margin: 0;">SECURITY DASHBOARD</h2>
                <button id="close-dashboard" style="background: #ff0000; color: white; border: none; padding: 5px 10px; cursor: pointer;">×</button>
            </div>
            <div style="margin-bottom: 20px;">
                <div>Status: <span style="color: ${this.securityLevel === 'CRITICAL' ? '#ff0000' : this.securityLevel === 'HIGH' ? '#ff6600' : '#00ff00'}">${this.securityLevel}</span></div>
                <div>Total Threats: ${this.threats.length}</div>
                <div>Active Monitoring: ON</div>
            </div>
            <div>
                <h3 style="font-size: 18px; margin-bottom: 10px;">Recent Threats:</h3>
                ${recentThreats.length > 0 ? recentThreats.map(threat => `
                    <div style="margin-bottom: 10px; padding: 10px; border: 1px solid #333; background: #111;">
                        <div style="color: ${threat.severity === 'CRITICAL' ? '#ff0000' : threat.severity === 'HIGH' ? '#ff6600' : '#ffff00'};">${threat.type} - ${threat.severity}</div>
                        <div style="font-size: 12px; color: #ccc;">${threat.description}</div>
                        <div style="font-size: 10px; color: #666;">${new Date(threat.timestamp).toLocaleString()}</div>
                    </div>
                `).join('') : '<div style="color: #666;">No recent threats detected</div>'}
            </div>
        `;

        document.body.appendChild(dashboard);

        document.getElementById('close-dashboard').addEventListener('click', () => {
            document.body.removeChild(dashboard);
        });
    }

    // Start periodic security scans
    startPeriodicScans() {
        setInterval(() => {
            this.performSecurityScan();
        }, 30000); // Every 30 seconds
    }

    // Perform security scan
    performSecurityScan() {
        // Check for suspicious DOM modifications
        this.scanDOMModifications();
        
        // Check for unauthorized scripts
        this.scanUnauthorizedScripts();
        
        // Update dashboard
        this.updateSecurityDashboard();
    }

    // Scan for DOM modifications
    scanDOMModifications() {
        const scripts = document.querySelectorAll('script');
        scripts.forEach(script => {
            if (script.src && !this.isAuthorizedScript(script.src)) {
                this.reportThreat(`Unauthorized script detected: ${script.src}`, 'UNAUTHORIZED_SCRIPT');
            }
        });
    }

    // Check if script is authorized
    isAuthorizedScript(src) {
        const authorizedDomains = [
            'cdn.tailwindcss.com',
            'www.gstatic.com',
            'cdnjs.cloudflare.com',
            'fonts.googleapis.com',
            window.location.hostname
        ];

        return authorizedDomains.some(domain => src.includes(domain)) || src.startsWith('./') || src.startsWith('/');
    }

    // Scan for unauthorized scripts
    scanUnauthorizedScripts() {
        // This would typically check for injected scripts
        // For now, we'll just log that the scan completed
        console.log('Security scan completed at', new Date().toISOString());
    }
}

// Initialize security monitor
document.addEventListener('DOMContentLoaded', () => {
    window.securityMonitor = new SecurityMonitor();
});

// Add pulse animation
const pulseStyles = document.createElement('style');
pulseStyles.textContent = `
    @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.5; }
        100% { opacity: 1; }
    }
`;
document.head.appendChild(pulseStyles);
