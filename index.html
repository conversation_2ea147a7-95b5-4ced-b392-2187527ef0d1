<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Wolf CTF Community - Secure Authentication</title>

  <!-- Security Meta Tags (Client-side only) -->
  <meta name="referrer" content="no-referrer">
  <meta name="robots" content="noindex, nofollow, noarchive, nosnippet, noimageindex">
  <meta name="googlebot" content="noindex, nofollow, noarchive, nosnippet, noimageindex">
  <meta name="format-detection" content="telephone=no, email=no, address=no">
  <!-- Note: X-Frame-Options, CSP, and other security headers must be set server-side -->

  <!-- Tailwind CSS Production Build -->
  <link rel="stylesheet" href="./tailwind-production.css">
  <!-- Google Fonts for bold, brutalist typography -->
  <link href="https://fonts.googleapis.com/css2?family=Bebas+Neue&family=Roboto+Mono:wght@400;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="./style.css">

  <!-- Firebase SDK with Enhanced Error Handling -->
  <script type="module">
    // Enhanced Firebase initialization with better error handling
    async function initializeFirebaseWithRetry() {
      const maxRetries = 3;
      let retryCount = 0;

      const firebaseConfig = {
        apiKey: "AIzaSyDN0E7ombncbYj-_iLhcEYxUGHb1FWo-6E",
        authDomain: "cyber-wolf-community-ctf.firebaseapp.com",
        projectId: "cyber-wolf-community-ctf",
        storageBucket: "cyber-wolf-community-ctf.firebasestorage.app",
        messagingSenderId: "370646269039",
        appId: "1:370646269039:web:e0566975c79d91d669219a",
        measurementId: "G-NH35HDL4N6"
      };

      while (retryCount < maxRetries) {
        try {
          console.log(`🔥 Initializing Firebase... (Attempt ${retryCount + 1}/${maxRetries})`);

          // Check network connectivity first
          await fetch('https://www.google.com/favicon.ico', {
            method: 'HEAD',
            mode: 'no-cors',
            cache: 'no-cache'
          });

          // Dynamic import with timeout
          const firebasePromise = Promise.race([
            import('https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js'),
            new Promise((_, reject) =>
              setTimeout(() => reject(new Error('Firebase SDK load timeout')), 10000)
            )
          ]);

          const authPromise = Promise.race([
            import('https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js'),
            new Promise((_, reject) =>
              setTimeout(() => reject(new Error('Firebase Auth SDK load timeout')), 10000)
            )
          ]);

          const [{ initializeApp }, { getAuth }] = await Promise.all([firebasePromise, authPromise]);

          const app = initializeApp(firebaseConfig);
          const auth = getAuth(app);

          // Test Firebase connection
          await new Promise((resolve, reject) => {
            const timeout = setTimeout(() => reject(new Error('Firebase connection timeout')), 5000);
            auth.onAuthStateChanged(() => {
              clearTimeout(timeout);
              resolve();
            }, reject);
          });

          // Make auth available globally
          window.firebaseAuth = auth;
          window.firebaseApp = app;

          // Dispatch custom event when Firebase is ready
          window.dispatchEvent(new CustomEvent('firebaseReady', {
            detail: { auth, app }
          }));

          console.log('✅ Firebase initialized successfully');
          return;

        } catch (error) {
          retryCount++;
          console.error(`❌ Firebase initialization failed (Attempt ${retryCount}):`, error);

          if (retryCount >= maxRetries) {
            // Final failure - dispatch error event
            window.dispatchEvent(new CustomEvent('firebaseError', {
              detail: {
                error: new Error(`Firebase initialization failed after ${maxRetries} attempts: ${error.message}`)
              }
            }));

            // Enable offline mode
            console.log('🔄 Enabling offline authentication mode...');
            window.dispatchEvent(new CustomEvent('enableOfflineMode'));
            return;
          }

          // Wait before retry with exponential backoff
          const delay = Math.min(1000 * Math.pow(2, retryCount - 1), 5000);
          console.log(`⏳ Retrying in ${delay}ms...`);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    // Start Firebase initialization
    initializeFirebaseWithRetry();
  </script>
</head>
<body class="min-h-screen flex items-center justify-center bg-gray-100">
  <div class="w-full max-w-md">
    <!-- Header -->
    <div class="text-center mb-8">
      <h1 class="text-4xl md:text-5xl mb-4 text-black">Wolf CTF Community</h1>
      <p class="text-lg text-gray-700 font-mono">Brutalist Authentication System</p>
    </div>

    <!-- Auth Container -->
    <div class="bg-white neo-brutalist p-8">
      <!-- Toggle Buttons -->
      <div class="flex mb-6">
        <button id="signInTab" class="flex-1 py-3 px-4 bg-black text-white font-bold text-lg border-r-2 border-black transition-all duration-200 hover:bg-gray-800">
          SIGN IN
        </button>
        <button id="signUpTab" class="flex-1 py-3 px-4 bg-gray-200 text-black font-bold text-lg transition-all duration-200 hover:bg-gray-300">
          SIGN UP
        </button>
      </div>

      <!-- Sign In Form -->
      <form id="signInForm" class="space-y-4">
        <div>
          <label class="block text-sm font-bold mb-2 text-black uppercase tracking-wide">
            <i class="fas fa-envelope mr-2"></i>Email Address
          </label>
          <input type="email" id="signInEmail" required
                 placeholder="Enter your email address"
                 class="auth-input w-full p-3 border-4 border-black font-mono text-lg focus:outline-none focus:ring-0 focus:border-gray-600 transition-all duration-300"
                 autocomplete="email">
        </div>
        <div>
          <label class="block text-sm font-bold mb-2 text-black uppercase tracking-wide">
            <i class="fas fa-lock mr-2"></i>Password
          </label>
          <input type="password" id="signInPassword" required
                 placeholder="Enter your password"
                 class="auth-input w-full p-3 border-4 border-black font-mono text-lg focus:outline-none focus:ring-0 focus:border-gray-600 transition-all duration-300"
                 autocomplete="current-password">
        </div>
        <button type="submit"
                class="auth-button w-full py-4 px-6 bg-yellow-400 text-black font-bold text-xl border-4 border-black shadow-[4px_4px_0_#000] hover:shadow-[2px_2px_0_#000] hover:translate-x-[2px] hover:translate-y-[2px] transition-all duration-200 uppercase tracking-wide">
          <i class="fas fa-sign-in-alt mr-2"></i>SIGN IN
        </button>
      </form>

      <!-- Sign Up Form (Hidden by default) -->
      <form id="signUpForm" class="space-y-4 hidden">
        <div>
          <label class="block text-sm font-bold mb-2 text-black uppercase tracking-wide">
            <i class="fas fa-user mr-2"></i>Full Name
          </label>
          <input type="text" id="signUpName" required
                 placeholder="Enter your full name"
                 minlength="2"
                 class="auth-input w-full p-3 border-4 border-black font-mono text-lg focus:outline-none focus:ring-0 focus:border-gray-600 transition-all duration-300"
                 autocomplete="name">
        </div>
        <div>
          <label class="block text-sm font-bold mb-2 text-black uppercase tracking-wide">
            <i class="fas fa-envelope mr-2"></i>Email Address
          </label>
          <input type="email" id="signUpEmail" required
                 placeholder="Enter your email address"
                 class="auth-input w-full p-3 border-4 border-black font-mono text-lg focus:outline-none focus:ring-0 focus:border-gray-600 transition-all duration-300"
                 autocomplete="email">
        </div>
        <div>
          <label class="block text-sm font-bold mb-2 text-black uppercase tracking-wide">
            <i class="fas fa-lock mr-2"></i>Password
          </label>
          <input type="password" id="signUpPassword" required
                 placeholder="Create a strong password"
                 minlength="8"
                 class="auth-input w-full p-3 border-4 border-black font-mono text-lg focus:outline-none focus:ring-0 focus:border-gray-600 transition-all duration-300"
                 autocomplete="new-password">
          <div class="text-xs text-gray-600 mt-1 font-mono">
            <strong>Requirements:</strong> 6+ chars, uppercase, lowercase, number, special character (@$!%*?&#+)
          </div>
        </div>
        <div>
          <label class="block text-sm font-bold mb-2 text-black uppercase tracking-wide">
            <i class="fas fa-lock mr-2"></i>Confirm Password
          </label>
          <input type="password" id="signUpConfirmPassword" required
                 placeholder="Confirm your password"
                 minlength="8"
                 class="auth-input w-full p-3 border-4 border-black font-mono text-lg focus:outline-none focus:ring-0 focus:border-gray-600 transition-all duration-300"
                 autocomplete="new-password">
        </div>
        <button type="submit"
                class="auth-button w-full py-4 px-6 bg-green-400 text-black font-bold text-xl border-4 border-black shadow-[4px_4px_0_#000] hover:shadow-[2px_2px_0_#000] hover:translate-x-[2px] hover:translate-y-[2px] transition-all duration-200 uppercase tracking-wide">
          <i class="fas fa-user-plus mr-2"></i>CREATE ACCOUNT
        </button>
      </form>

      <!-- Error/Success Messages -->
      <div id="messageContainer" class="mt-4 hidden">
        <div id="messageBox" class="p-4 border-4 border-black font-bold text-center uppercase tracking-wide">
        </div>
      </div>

      <!-- Offline Mode Notice -->
      <div id="offlineNotice" class="mt-4 p-4 bg-yellow-100 border-4 border-yellow-500 hidden">
        <div class="flex items-center justify-center">
          <i class="fas fa-wifi-slash mr-2 text-yellow-600"></i>
          <span class="font-bold text-yellow-800">OFFLINE MODE ACTIVE</span>
        </div>
        <p class="text-sm text-yellow-700 mt-2 text-center">
          Firebase connection failed. Using local authentication for testing.
        </p>
      </div>
    </div>

    <!-- Footer -->
    <div class="text-center mt-8">
      <p class="text-gray-600 font-mono text-sm">© 2024 Neo Brutalist Auth System</p>
    </div>
  </div>

  <script src="./fetch-manager.js"></script>
  <script src="./error-handler.js"></script>
  <script src="./offline-auth.js"></script>
  <script src="./network-diagnostics.js"></script>
  <script src="./firebase-status.js"></script>
  <script src="./security.js"></script>
  <script src="./security-monitor.js"></script>
  <script type="module" src="./auth.js"></script>

  <!-- Offline Mode Handler -->
  <script>
    // Handle offline mode activation
    window.addEventListener('enableOfflineMode', () => {
      console.log('🔄 Offline mode activated');
      const offlineNotice = document.getElementById('offlineNotice');
      if (offlineNotice) {
        offlineNotice.classList.remove('hidden');
      }

      // Enable offline authentication
      if (window.offlineAuth) {
        window.offlineAuth.enable();
      }
    });

    // Network status monitoring
    window.addEventListener('online', () => {
      console.log('🌐 Network connection restored');
      const offlineNotice = document.getElementById('offlineNotice');
      if (offlineNotice) {
        offlineNotice.classList.add('hidden');
      }

      // Attempt to reconnect Firebase
      setTimeout(() => {
        window.location.reload();
      }, 1000);
    });

    window.addEventListener('offline', () => {
      console.log('📡 Network connection lost');
      window.dispatchEvent(new CustomEvent('enableOfflineMode'));
    });
  </script>

  <!-- Development Testing (remove in production) -->
  <script>
    if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
      const authTestScript = document.createElement('script');
      authTestScript.src = './auth-test.js';
      document.head.appendChild(authTestScript);

      const firebaseTestScript = document.createElement('script');
      firebaseTestScript.src = './firebase-test.js';
      document.head.appendChild(firebaseTestScript);
    }
  </script>
</body>
</html>
