/* Tailwind CSS Production Build - Wolf CTF Community */
/* This file contains only the classes used in the project */

/* Base styles */
* {
  box-sizing: border-box;
  border-width: 0;
  border-style: solid;
  border-color: #e5e7eb;
}

html {
  line-height: 1.5;
  -webkit-text-size-adjust: 100%;
  -moz-tab-size: 4;
  tab-size: 4;
  font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
}

body {
  margin: 0;
  line-height: inherit;
}

/* Utility classes used in the project */
.min-h-screen { min-height: 100vh; }
.flex { display: flex; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.w-full { width: 100%; }
.max-w-md { max-width: 28rem; }
.max-w-sm { max-width: 24rem; }
.max-w-xs { max-width: 20rem; }
.max-w-4xl { max-width: 56rem; }
.mx-auto { margin-left: auto; margin-right: auto; }
.mb-4 { margin-bottom: 1rem; }
.mb-6 { margin-bottom: 1.5rem; }
.mb-8 { margin-bottom: 2rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-4 { margin-top: 1rem; }
.mt-6 { margin-top: 1.5rem; }
.mr-2 { margin-right: 0.5rem; }
.p-4 { padding: 1rem; }
.p-8 { padding: 2rem; }
.py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem; }
.py-4 { padding-top: 1rem; padding-bottom: 1rem; }
.px-4 { padding-left: 1rem; padding-right: 1rem; }
.px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
.p-3 { padding: 0.75rem; }
.text-center { text-align: center; }
.text-4xl { font-size: 2.25rem; line-height: 2.5rem; }
.text-5xl { font-size: 3rem; line-height: 1; }
.text-lg { font-size: 1.125rem; line-height: 1.75rem; }
.text-xl { font-size: 1.25rem; line-height: 1.75rem; }
.text-2xl { font-size: 1.5rem; line-height: 2rem; }
.text-sm { font-size: 0.875rem; line-height: 1.25rem; }
.text-xs { font-size: 0.75rem; line-height: 1rem; }
.font-bold { font-weight: 700; }
.font-mono { font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace; }
.uppercase { text-transform: uppercase; }
.tracking-wide { letter-spacing: 0.025em; }
.text-black { color: rgb(0 0 0); }
.text-white { color: rgb(255 255 255); }
.text-gray-700 { color: rgb(55 65 81); }
.text-gray-600 { color: rgb(75 85 99); }
.text-red-600 { color: rgb(220 38 38); }
.text-green-600 { color: rgb(22 163 74); }
.text-blue-600 { color: rgb(37 99 235); }
.text-yellow-600 { color: rgb(202 138 4); }
.bg-gray-100 { background-color: rgb(243 244 246); }
.bg-gray-200 { background-color: rgb(229 231 235); }
.bg-white { background-color: rgb(255 255 255); }
.bg-black { background-color: rgb(0 0 0); }
.bg-yellow-400 { background-color: rgb(250 204 21); }
.bg-green-400 { background-color: rgb(74 222 128); }
.bg-red-400 { background-color: rgb(248 113 113); }
.bg-blue-400 { background-color: rgb(96 165 250); }
.bg-red-200 { background-color: rgb(254 202 202); }
.bg-green-200 { background-color: rgb(187 247 208); }
.bg-blue-200 { background-color: rgb(191 219 254); }
.bg-yellow-200 { background-color: rgb(254 240 138); }
.bg-red-100 { background-color: rgb(254 226 226); }
.bg-green-100 { background-color: rgb(220 252 231); }
.bg-blue-100 { background-color: rgb(219 234 254); }
.bg-yellow-100 { background-color: rgb(254 249 195); }
.border-4 { border-width: 4px; }
.border-2 { border-width: 2px; }
.border-black { border-color: rgb(0 0 0); }
.border-red-600 { border-color: rgb(220 38 38); }
.border-green-600 { border-color: rgb(22 163 74); }
.border-blue-600 { border-color: rgb(37 99 235); }
.border-yellow-600 { border-color: rgb(202 138 4); }
.border-r-2 { border-right-width: 2px; }
.border-b-4 { border-bottom-width: 4px; }
.space-y-4 > :not([hidden]) ~ :not([hidden]) { margin-top: 1rem; }
.space-y-3 > :not([hidden]) ~ :not([hidden]) { margin-top: 0.75rem; }
.block { display: block; }
.inline-block { display: inline-block; }
.hidden { display: none; }
.flex-1 { flex: 1 1 0%; }
.grid { display: grid; }
.gap-2 { gap: 0.5rem; }
.gap-8 { gap: 2rem; }
.h-2 { height: 0.5rem; }
.transition-all { transition-property: all; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
.transition-colors { transition-property: color, background-color, border-color, text-decoration-color, fill, stroke; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
.duration-200 { transition-duration: 200ms; }
.duration-300 { transition-duration: 300ms; }
.cursor-pointer { cursor: pointer; }
.focus\:outline-none:focus { outline: 2px solid transparent; outline-offset: 2px; }
.focus\:ring-0:focus { --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color); --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color); box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000); }
.focus\:border-gray-600:focus { border-color: rgb(75 85 99); }
.hover\:bg-gray-800:hover { background-color: rgb(31 41 55); }
.hover\:bg-gray-300:hover { background-color: rgb(209 213 219); }

/* Custom shadow utilities */
.shadow-\[4px_4px_0_\#000\] { box-shadow: 4px 4px 0 #000; }
.shadow-\[2px_2px_0_\#000\] { box-shadow: 2px 2px 0 #000; }
.shadow-\[6px_6px_0_\#000\] { box-shadow: 6px 6px 0 #000; }
.shadow-\[8px_8px_0_\#000\] { box-shadow: 8px 8px 0 #000; }

/* Custom transform utilities */
.translate-x-\[2px\] { transform: translateX(2px); }
.translate-y-\[2px\] { transform: translateY(2px); }
.hover\:translate-x-\[2px\]:hover { transform: translateX(2px); }
.hover\:translate-y-\[2px\]:hover { transform: translateY(2px); }
.hover\:shadow-\[2px_2px_0_\#000\]:hover { box-shadow: 2px 2px 0 #000; }

/* Responsive utilities */
@media (min-width: 768px) {
  .md\:text-5xl { font-size: 3rem; line-height: 1; }
  .md\:p-8 { padding: 2rem; }
  .md\:mb-8 { margin-bottom: 2rem; }
  .md\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
}

/* Form input styles */
input[type="email"], input[type="password"], input[type="text"] {
  appearance: none;
  background-color: transparent;
}

input[type="email"]:focus, input[type="password"]:focus, input[type="text"]:focus {
  outline: none;
  border-color: rgb(75 85 99);
}

/* Button styles */
button {
  cursor: pointer;
  border: none;
  background: none;
  padding: 0;
  font: inherit;
}

button:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

/* Custom animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from { transform: translateX(100%); }
  to { transform: translateX(0); }
}

.animate-fadeIn { animation: fadeIn 0.3s ease-in-out; }
.animate-slideIn { animation: slideIn 0.3s ease-out; }

/* Neo-brutalist specific styles */
.neo-brutalist {
  border: 4px solid #000;
  box-shadow: 8px 8px 0 #000;
  transition: all 0.2s ease;
}

.neo-brutalist:hover {
  box-shadow: 6px 6px 0 #000;
  transform: translate(2px, 2px);
}

/* Component specific styles */
.component-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.component-preview {
  flex-grow: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1.5rem;
  min-height: 200px;
}

/* Authentication specific styles */
.auth-input:focus {
  outline: none;
  border-color: #4a5568;
  box-shadow: 0 0 0 3px rgba(74, 85, 104, 0.1);
}

.auth-button {
  transition: all 0.2s ease;
  transform: translate(0, 0);
}

.auth-button:hover {
  transform: translate(2px, 2px);
}

.auth-button:active {
  transform: translate(0, 0);
}

/* Utility for hiding scrollbars */
.no-scrollbar::-webkit-scrollbar {
  display: none;
}

.no-scrollbar {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

/* Print styles */
@media print {
  .no-print { display: none !important; }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .border-black { border-color: #000; }
  .text-black { color: #000; }
  .bg-white { background-color: #fff; }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .transition-all { transition: none; }
  .transition-colors { transition: none; }
  * { animation-duration: 0.01ms !important; animation-iteration-count: 1 !important; }
}
