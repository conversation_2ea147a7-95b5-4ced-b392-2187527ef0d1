// Firebase Connection Manager - Enhanced Firebase Connection and Authentication System
// Comprehensive solution for Firebase connectivity, authentication, and error handling

class FirebaseConnectionManager {
    constructor() {
        this.isConnected = false;
        this.isInitialized = false;
        this.connectionAttempts = 0;
        this.maxConnectionAttempts = 5;
        this.retryDelay = 2000;
        this.auth = null;
        this.app = null;
        this.connectionStatus = 'disconnected';
        this.statusCallbacks = [];
        this.diagnostics = {
            lastCheck: null,
            results: [],
            issues: []
        };
        
        this.init();
    }

    init() {
        console.log('🔥 Initializing Firebase Connection Manager...');
        this.createStatusIndicator();
        this.setupEventListeners();
        this.startConnectionMonitoring();
    }

    // Enhanced Firebase Connection Verification
    async verifyFirebaseConnection() {
        console.log('🔍 Verifying Firebase connection...');
        this.updateStatus('connecting', 'Verifying Firebase connection...');
        
        try {
            // Step 1: Check if Firebase SDK is loaded
            if (!this.checkFirebaseSDK()) {
                throw new Error('Firebase SDK not loaded');
            }

            // Step 2: Verify Firebase app initialization
            if (!this.verifyFirebaseApp()) {
                throw new Error('Firebase app not initialized');
            }

            // Step 3: Test authentication service
            if (!this.verifyAuthService()) {
                throw new Error('Firebase Auth service not available');
            }

            // Step 4: Test network connectivity to Firebase
            await this.testFirebaseEndpoints();

            // Step 5: Verify configuration
            this.verifyFirebaseConfig();

            this.isConnected = true;
            this.isInitialized = true;
            this.connectionStatus = 'connected';
            this.updateStatus('connected', 'Firebase connection verified');
            
            console.log('✅ Firebase connection verification successful');
            this.notifyStatusCallbacks('connected');
            
            return true;

        } catch (error) {
            console.error('❌ Firebase connection verification failed:', error);
            this.handleConnectionError(error);
            return false;
        }
    }

    checkFirebaseSDK() {
        console.log('📦 Checking Firebase SDK...');
        
        // Check if Firebase modules are available globally
        const hasFirebaseApp = typeof window.firebaseApp !== 'undefined';
        const hasFirebaseAuth = typeof window.firebaseAuth !== 'undefined';
        
        // Check if Firebase functions are available
        const hasInitializeApp = typeof window.initializeApp !== 'undefined';
        const hasGetAuth = typeof window.getAuth !== 'undefined';
        
        if (!hasFirebaseApp && !hasFirebaseAuth && !hasInitializeApp && !hasGetAuth) {
            this.diagnostics.issues.push('Firebase SDK not loaded - check CDN access');
            return false;
        }
        
        console.log('✅ Firebase SDK loaded successfully');
        return true;
    }

    verifyFirebaseApp() {
        console.log('🔧 Verifying Firebase app...');
        
        if (window.firebaseApp) {
            this.app = window.firebaseApp;
            console.log('✅ Firebase app found');
            return true;
        }
        
        // Try to get app from Firebase
        try {
            if (typeof firebase !== 'undefined' && firebase.apps && firebase.apps.length > 0) {
                this.app = firebase.apps[0];
                console.log('✅ Firebase app retrieved from firebase.apps');
                return true;
            }
        } catch (error) {
            console.log('⚠️ Could not retrieve Firebase app from firebase.apps');
        }
        
        this.diagnostics.issues.push('Firebase app not initialized');
        return false;
    }

    verifyAuthService() {
        console.log('🔐 Verifying Firebase Auth service...');
        
        if (window.firebaseAuth) {
            this.auth = window.firebaseAuth;
            console.log('✅ Firebase Auth service found');
            return true;
        }
        
        // Try to initialize auth if app is available
        if (this.app) {
            try {
                // This would need to be imported properly in a real scenario
                console.log('🔄 Attempting to initialize Auth service...');
                // In the actual implementation, this would use the imported getAuth function
                this.diagnostics.issues.push('Auth service initialization needed');
                return false;
            } catch (error) {
                console.error('❌ Failed to initialize Auth service:', error);
            }
        }
        
        this.diagnostics.issues.push('Firebase Auth service not available');
        return false;
    }

    async testFirebaseEndpoints() {
        console.log('🌐 Testing Firebase endpoints...');
        
        const endpoints = [
            'https://firebase.googleapis.com/',
            'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js',
            'https://identitytoolkit.googleapis.com/'
        ];
        
        const results = await Promise.allSettled(
            endpoints.map(endpoint => this.testEndpoint(endpoint))
        );
        
        const failedEndpoints = results
            .map((result, index) => ({ result, endpoint: endpoints[index] }))
            .filter(({ result }) => result.status === 'rejected')
            .map(({ endpoint }) => endpoint);
        
        if (failedEndpoints.length > 0) {
            this.diagnostics.issues.push(`Unreachable endpoints: ${failedEndpoints.join(', ')}`);
            throw new Error(`Firebase endpoints unreachable: ${failedEndpoints.length}/${endpoints.length} failed`);
        }
        
        console.log('✅ All Firebase endpoints accessible');
    }

    async testEndpoint(url) {
        try {
            const response = await fetch(url, {
                method: 'HEAD',
                mode: 'no-cors',
                cache: 'no-cache',
                signal: AbortSignal.timeout(5000)
            });
            return true;
        } catch (error) {
            console.warn(`❌ Endpoint test failed for ${url}:`, error.message);
            throw error;
        }
    }

    verifyFirebaseConfig() {
        console.log('⚙️ Verifying Firebase configuration...');
        
        if (!this.app || !this.app.options) {
            this.diagnostics.issues.push('Firebase configuration not accessible');
            return false;
        }
        
        const config = this.app.options;
        const requiredFields = ['apiKey', 'authDomain', 'projectId'];
        const missingFields = requiredFields.filter(field => !config[field]);
        
        if (missingFields.length > 0) {
            this.diagnostics.issues.push(`Missing config fields: ${missingFields.join(', ')}`);
            return false;
        }
        
        // Verify project ID matches expected
        if (config.projectId !== 'cyber-wolf-community-ctf') {
            this.diagnostics.issues.push(`Unexpected project ID: ${config.projectId}`);
            console.warn('⚠️ Project ID mismatch - verify configuration');
        }
        
        console.log('✅ Firebase configuration verified');
        return true;
    }

    handleConnectionError(error) {
        this.isConnected = false;
        this.connectionStatus = 'error';
        
        let errorMessage = 'Firebase connection failed';
        let errorType = 'unknown';
        
        if (error.message.includes('SDK not loaded')) {
            errorType = 'sdk';
            errorMessage = 'Firebase SDK loading failed - check internet connection';
        } else if (error.message.includes('not initialized')) {
            errorType = 'initialization';
            errorMessage = 'Firebase initialization failed - check configuration';
        } else if (error.message.includes('Auth service')) {
            errorType = 'auth';
            errorMessage = 'Firebase Auth service unavailable';
        } else if (error.message.includes('endpoints')) {
            errorType = 'network';
            errorMessage = 'Firebase servers unreachable - check network/firewall';
        }
        
        this.updateStatus('error', errorMessage);
        this.notifyStatusCallbacks('error', { error, type: errorType });
        
        // Auto-retry with exponential backoff
        if (this.connectionAttempts < this.maxConnectionAttempts) {
            this.scheduleRetry();
        } else {
            this.updateStatus('failed', 'Connection failed - manual retry required');
            this.showConnectionHelp();
        }
    }

    scheduleRetry() {
        this.connectionAttempts++;
        const delay = this.retryDelay * Math.pow(2, this.connectionAttempts - 1);
        
        console.log(`🔄 Scheduling retry ${this.connectionAttempts}/${this.maxConnectionAttempts} in ${delay}ms`);
        this.updateStatus('retrying', `Retrying connection in ${Math.ceil(delay/1000)}s...`);
        
        setTimeout(() => {
            this.verifyFirebaseConnection();
        }, delay);
    }

    // Enhanced Authentication Testing
    async testAuthentication() {
        console.log('🔐 Testing Firebase Authentication...');
        
        if (!this.auth) {
            throw new Error('Firebase Auth not available');
        }
        
        try {
            // Test auth state listener
            const testPromise = new Promise((resolve, reject) => {
                const timeout = setTimeout(() => {
                    reject(new Error('Auth state listener timeout'));
                }, 5000);
                
                const unsubscribe = this.auth.onAuthStateChanged((user) => {
                    clearTimeout(timeout);
                    unsubscribe();
                    resolve(user);
                }, (error) => {
                    clearTimeout(timeout);
                    unsubscribe();
                    reject(error);
                });
            });
            
            await testPromise;
            console.log('✅ Firebase Authentication service working');
            return true;
            
        } catch (error) {
            console.error('❌ Firebase Authentication test failed:', error);
            throw error;
        }
    }

    // Connection Status Management
    updateStatus(status, message) {
        this.connectionStatus = status;
        
        if (this.statusIndicator) {
            this.statusIndicator.updateStatus(status, message);
        }
        
        // Update diagnostics
        this.diagnostics.lastCheck = new Date().toISOString();
        this.diagnostics.results.push({
            timestamp: this.diagnostics.lastCheck,
            status,
            message
        });
        
        // Keep only last 10 results
        if (this.diagnostics.results.length > 10) {
            this.diagnostics.results = this.diagnostics.results.slice(-10);
        }
    }

    createStatusIndicator() {
        // Create enhanced status indicator
        this.statusIndicator = {
            element: null,
            
            create() {
                this.element = document.createElement('div');
                this.element.id = 'firebase-connection-status';
                this.element.style.cssText = `
                    position: fixed;
                    top: 10px;
                    right: 10px;
                    background: #1a1a1a;
                    color: #fff;
                    padding: 12px 16px;
                    font-family: 'Roboto Mono', monospace;
                    font-size: 12px;
                    border: 2px solid #333;
                    border-radius: 6px;
                    z-index: 9999;
                    transition: all 0.3s ease;
                    cursor: pointer;
                    user-select: none;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                    min-width: 200px;
                `;
                
                document.body.appendChild(this.element);
                
                // Click handler for detailed info
                this.element.addEventListener('click', () => {
                    window.firebaseConnectionManager.showDetailedStatus();
                });
            },
            
            updateStatus(status, message) {
                if (!this.element) this.create();
                
                let backgroundColor, borderColor, icon;
                
                switch (status) {
                    case 'connecting':
                        backgroundColor = '#ff8c00';
                        borderColor = '#ff6600';
                        icon = '🔄';
                        break;
                    case 'connected':
                        backgroundColor = '#00aa00';
                        borderColor = '#008800';
                        icon = '✅';
                        break;
                    case 'error':
                        backgroundColor = '#ff4444';
                        borderColor = '#cc0000';
                        icon = '❌';
                        break;
                    case 'retrying':
                        backgroundColor = '#ffaa00';
                        borderColor = '#cc8800';
                        icon = '🔄';
                        break;
                    case 'failed':
                        backgroundColor = '#cc0000';
                        borderColor = '#990000';
                        icon = '💥';
                        break;
                    default:
                        backgroundColor = '#333';
                        borderColor = '#666';
                        icon = '❓';
                }
                
                this.element.style.backgroundColor = backgroundColor;
                this.element.style.borderColor = borderColor;
                this.element.innerHTML = `
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <span style="font-size: 16px;">${icon}</span>
                        <div>
                            <div style="font-weight: bold;">Firebase Status</div>
                            <div style="font-size: 10px; opacity: 0.8;">${message}</div>
                        </div>
                    </div>
                `;
                
                // Auto-hide after successful connection
                if (status === 'connected') {
                    setTimeout(() => {
                        if (this.element) {
                            this.element.style.opacity = '0.3';
                            this.element.style.transform = 'scale(0.8)';
                        }
                    }, 3000);
                }
            }
        };
        
        this.statusIndicator.create();
    }

    showDetailedStatus() {
        const modal = document.createElement('div');
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
            font-family: 'Roboto Mono', monospace;
        `;
        
        const issuesHtml = this.diagnostics.issues.length > 0 
            ? `<div style="margin-top: 15px;">
                <div style="color: #ff6600; font-weight: bold; margin-bottom: 8px;">🔧 Issues Found:</div>
                ${this.diagnostics.issues.map(issue => `<div style="color: #ffaa00; font-size: 11px; margin-bottom: 4px;">• ${issue}</div>`).join('')}
               </div>`
            : '<div style="margin-top: 15px; color: #00aa00;">✅ No issues detected</div>';
        
        const recentResults = this.diagnostics.results.slice(-5).reverse();
        const resultsHtml = recentResults.length > 0
            ? `<div style="margin-top: 15px;">
                <div style="color: #00aaff; font-weight: bold; margin-bottom: 8px;">📊 Recent Status:</div>
                ${recentResults.map(result => `
                    <div style="font-size: 10px; margin-bottom: 4px; color: #ccc;">
                        ${new Date(result.timestamp).toLocaleTimeString()}: ${result.message}
                    </div>
                `).join('')}
               </div>`
            : '';
        
        modal.innerHTML = `
            <div style="background: #1a1a1a; color: #fff; padding: 30px; border: 3px solid #333; border-radius: 8px; max-width: 500px; width: 90%;">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                    <h2 style="font-size: 18px; margin: 0; color: #00aaff;">🔥 Firebase Connection Status</h2>
                    <button id="close-status" style="background: #ff4444; color: white; border: none; padding: 8px 12px; border-radius: 4px; cursor: pointer;">×</button>
                </div>
                
                <div style="margin-bottom: 15px;">
                    <div style="color: #ccc; font-size: 12px;">Current Status:</div>
                    <div style="color: ${this.connectionStatus === 'connected' ? '#00aa00' : '#ff6600'}; font-weight: bold; font-size: 14px;">
                        ${this.connectionStatus.toUpperCase()}
                    </div>
                </div>
                
                <div style="margin-bottom: 15px;">
                    <div style="color: #ccc; font-size: 12px;">Connection Attempts:</div>
                    <div style="color: #fff;">${this.connectionAttempts}/${this.maxConnectionAttempts}</div>
                </div>
                
                ${issuesHtml}
                ${resultsHtml}
                
                <div style="margin-top: 20px; display: flex; gap: 10px;">
                    <button id="retry-connection" style="background: #00aa00; color: white; border: none; padding: 10px 15px; border-radius: 4px; cursor: pointer; font-size: 12px;">
                        🔄 Retry Connection
                    </button>
                    <button id="run-diagnostics" style="background: #0066ff; color: white; border: none; padding: 10px 15px; border-radius: 4px; cursor: pointer; font-size: 12px;">
                        🔧 Run Diagnostics
                    </button>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        // Event handlers
        modal.querySelector('#close-status').addEventListener('click', () => {
            document.body.removeChild(modal);
        });
        
        modal.querySelector('#retry-connection').addEventListener('click', () => {
            document.body.removeChild(modal);
            this.retryConnection();
        });
        
        modal.querySelector('#run-diagnostics').addEventListener('click', () => {
            document.body.removeChild(modal);
            if (window.networkDiagnostics) {
                window.networkDiagnostics.runDiagnostics();
            }
        });
        
        // Close on background click
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                document.body.removeChild(modal);
            }
        });
    }

    showConnectionHelp() {
        // Implementation for connection help modal
        console.log('🆘 Showing connection help...');
        // This would show a help modal with troubleshooting steps
    }

    // Event Listeners and Monitoring
    setupEventListeners() {
        // Listen for Firebase events
        window.addEventListener('firebaseReady', (event) => {
            console.log('✅ Firebase ready event received');
            this.auth = event.detail.auth;
            this.app = event.detail.app;
            this.verifyFirebaseConnection();
        });
        
        window.addEventListener('firebaseError', (event) => {
            console.error('❌ Firebase error event received:', event.detail.error);
            this.handleConnectionError(event.detail.error);
        });
        
        // Network status monitoring
        window.addEventListener('online', () => {
            console.log('🌐 Network connection restored');
            this.retryConnection();
        });
        
        window.addEventListener('offline', () => {
            console.log('📡 Network connection lost');
            this.updateStatus('offline', 'Network offline');
        });
    }

    startConnectionMonitoring() {
        // Initial connection check
        setTimeout(() => {
            if (!this.isConnected) {
                this.verifyFirebaseConnection();
            }
        }, 1000);
        
        // Periodic health checks
        setInterval(() => {
            if (this.isConnected) {
                this.performHealthCheck();
            }
        }, 30000); // Check every 30 seconds
    }

    async performHealthCheck() {
        try {
            if (this.auth && this.auth.currentUser !== undefined) {
                // Auth service is responsive
                return true;
            }
        } catch (error) {
            console.warn('⚠️ Health check failed:', error);
            this.verifyFirebaseConnection();
        }
    }

    retryConnection() {
        this.connectionAttempts = 0;
        this.diagnostics.issues = [];
        this.verifyFirebaseConnection();
    }

    // Public API
    onStatusChange(callback) {
        this.statusCallbacks.push(callback);
    }

    notifyStatusCallbacks(status, data = {}) {
        this.statusCallbacks.forEach(callback => {
            try {
                callback(status, data);
            } catch (error) {
                console.error('Status callback error:', error);
            }
        });
    }

    getConnectionStatus() {
        return {
            isConnected: this.isConnected,
            isInitialized: this.isInitialized,
            status: this.connectionStatus,
            attempts: this.connectionAttempts,
            diagnostics: this.diagnostics
        };
    }

    // Test functions for manual verification
    async runConnectionTest() {
        console.log('🧪 Running comprehensive connection test...');
        
        try {
            await this.verifyFirebaseConnection();
            await this.testAuthentication();
            
            console.log('✅ All connection tests passed');
            return true;
        } catch (error) {
            console.error('❌ Connection test failed:', error);
            return false;
        }
    }
}

// Initialize Firebase Connection Manager
document.addEventListener('DOMContentLoaded', () => {
    window.firebaseConnectionManager = new FirebaseConnectionManager();
    
    // Make test functions available globally
    window.testFirebaseConnection = () => {
        return window.firebaseConnectionManager.runConnectionTest();
    };
    
    window.getFirebaseStatus = () => {
        return window.firebaseConnectionManager.getConnectionStatus();
    };
    
    console.log('🔥 Firebase Connection Manager loaded. Use testFirebaseConnection() to run tests.');
});