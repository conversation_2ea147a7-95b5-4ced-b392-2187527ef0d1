// Enhanced Error Handling System for Wolf CTF Community
// Provides comprehensive error logging and user feedback

class ErrorHandler {
    constructor() {
        this.errors = [];
        this.maxErrors = 100;
        this.isProduction = window.location.hostname !== 'localhost';
        this.init();
    }

    init() {
        this.setupGlobalErrorHandling();
        this.setupConsoleErrorCapture();
        this.setupNetworkErrorHandling();
        this.setupPromiseRejectionHandling();
    }

    // Setup global error handling
    setupGlobalErrorHandling() {
        window.addEventListener('error', (event) => {
            this.handleError({
                type: 'JavaScript Error',
                message: event.message,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                stack: event.error ? event.error.stack : null,
                timestamp: new Date().toISOString()
            });
        });
    }

    // Setup console error capture
    setupConsoleErrorCapture() {
        const originalError = console.error;
        console.error = (...args) => {
            // Log to our system
            this.handleError({
                type: 'Console <PERSON>r',
                message: args.join(' '),
                timestamp: new Date().toISOString()
            });
            
            // Call original console.error in development
            if (!this.isProduction) {
                originalError.apply(console, args);
            }
        };
    }

    // Setup network error handling (now handled by fetch manager)
    setupNetworkErrorHandling() {
        // Network error handling is now managed by the centralized fetch manager
        // This method is kept for compatibility but functionality moved to fetch-manager.js
        console.log('📡 Network error handling delegated to fetch manager');
    }

    // Setup promise rejection handling
    setupPromiseRejectionHandling() {
        window.addEventListener('unhandledrejection', (event) => {
            // Suppress external API errors
            if (this.isExternalAPIError(event.reason)) {
                event.preventDefault();
                return;
            }

            this.handleError({
                type: 'Unhandled Promise Rejection',
                message: event.reason ? event.reason.toString() : 'Unknown promise rejection',
                timestamp: new Date().toISOString()
            });
        });
    }

    // Check if error is from external API
    isExternalAPIError(error) {
        const errorString = error ? error.toString() : '';
        const externalAPIs = [
            'gapi',
            'google',
            'googleapis',
            'gstatic.com',
            'firebase',
            'tailwindcss'
        ];
        
        return externalAPIs.some(api => errorString.toLowerCase().includes(api));
    }

    // Handle error
    handleError(errorInfo) {
        // Skip external API errors
        if (this.isExternalAPIError(errorInfo.message)) {
            return;
        }

        // Add to error log
        this.errors.push(errorInfo);
        
        // Keep only recent errors
        if (this.errors.length > this.maxErrors) {
            this.errors = this.errors.slice(-this.maxErrors);
        }

        // Store in localStorage
        this.saveErrorsToStorage();

        // Log to console in development
        if (!this.isProduction) {
            console.warn('🚨 Error captured:', errorInfo);
        }

        // Show user-friendly error message for critical errors
        if (this.isCriticalError(errorInfo)) {
            this.showUserErrorMessage(errorInfo);
        }

        // Send to analytics/monitoring service (if configured)
        this.sendToMonitoring(errorInfo);
    }

    // Check if error is critical
    isCriticalError(errorInfo) {
        const criticalKeywords = [
            'firebase',
            'authentication',
            'network',
            'security',
            'unauthorized'
        ];
        
        const message = errorInfo.message.toLowerCase();
        return criticalKeywords.some(keyword => message.includes(keyword));
    }

    // Show user-friendly error message
    showUserErrorMessage(errorInfo) {
        // Don't show multiple error messages
        if (document.querySelector('.error-notification')) {
            return;
        }

        const errorNotification = document.createElement('div');
        errorNotification.className = 'error-notification';
        errorNotification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #ff4444;
            color: white;
            padding: 15px 20px;
            border: 4px solid #000;
            box-shadow: 4px 4px 0 #000;
            font-family: 'Roboto Mono', monospace;
            font-weight: bold;
            z-index: 10000;
            max-width: 350px;
            animation: slideInRight 0.3s ease-out;
        `;

        let userMessage = 'An error occurred. Please try again.';
        
        if (errorInfo.type === 'Network Error') {
            userMessage = 'Network connection issue. Please check your internet connection.';
        } else if (errorInfo.message.includes('firebase')) {
            userMessage = 'Authentication service temporarily unavailable. Please try again later.';
        }

        errorNotification.innerHTML = `
            <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 10px;">
                <i class="fas fa-exclamation-triangle"></i>
                <span style="font-size: 16px;">System Error</span>
                <button onclick="this.parentElement.parentElement.remove()" style="margin-left: auto; background: none; border: none; color: white; font-size: 18px; cursor: pointer;">×</button>
            </div>
            <div style="font-size: 14px;">${userMessage}</div>
            <div style="font-size: 12px; margin-top: 10px; opacity: 0.8;">Error ID: ${Date.now()}</div>
        `;

        document.body.appendChild(errorNotification);

        // Auto-remove after 10 seconds
        setTimeout(() => {
            if (document.body.contains(errorNotification)) {
                document.body.removeChild(errorNotification);
            }
        }, 10000);
    }

    // Save errors to localStorage
    saveErrorsToStorage() {
        try {
            localStorage.setItem('wolfctf_errors', JSON.stringify(this.errors));
        } catch (e) {
            // Handle localStorage quota exceeded
            this.errors = this.errors.slice(-50); // Keep only last 50 errors
            try {
                localStorage.setItem('wolfctf_errors', JSON.stringify(this.errors));
            } catch (e2) {
                // If still failing, clear error storage
                localStorage.removeItem('wolfctf_errors');
            }
        }
    }

    // Send to monitoring service
    sendToMonitoring(errorInfo) {
        // In production, you would send this to your monitoring service
        // For now, we'll just log it
        if (this.isProduction) {
            // Example: Send to monitoring service
            // fetch('/api/errors', {
            //     method: 'POST',
            //     headers: { 'Content-Type': 'application/json' },
            //     body: JSON.stringify(errorInfo)
            // }).catch(() => {}); // Silently fail if monitoring is down
        }
    }

    // Get error statistics
    getErrorStats() {
        const stats = {
            total: this.errors.length,
            byType: {},
            recent: this.errors.filter(e => 
                Date.now() - new Date(e.timestamp).getTime() < 24 * 60 * 60 * 1000
            ).length
        };

        this.errors.forEach(error => {
            stats.byType[error.type] = (stats.byType[error.type] || 0) + 1;
        });

        return stats;
    }

    // Clear error log
    clearErrors() {
        this.errors = [];
        localStorage.removeItem('wolfctf_errors');
    }

    // Export errors for debugging
    exportErrors() {
        const errorData = {
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent,
            url: window.location.href,
            errors: this.errors,
            stats: this.getErrorStats()
        };

        const blob = new Blob([JSON.stringify(errorData, null, 2)], {
            type: 'application/json'
        });
        
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `wolfctf-errors-${Date.now()}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    // Load errors from storage
    loadErrorsFromStorage() {
        try {
            const stored = localStorage.getItem('wolfctf_errors');
            if (stored) {
                this.errors = JSON.parse(stored);
            }
        } catch (e) {
            // Invalid JSON, clear storage
            localStorage.removeItem('wolfctf_errors');
            this.errors = [];
        }
    }
}

// Initialize error handler
document.addEventListener('DOMContentLoaded', () => {
    window.errorHandler = new ErrorHandler();
    
    // Load existing errors
    window.errorHandler.loadErrorsFromStorage();
    
    // Add global error handler reference for debugging
    if (!window.errorHandler.isProduction) {
        console.log('🛡️ Security: Error handler initialized. Use window.errorHandler for debugging.');
    }
});

// Add CSS for error notifications
const errorStyles = document.createElement('style');
errorStyles.textContent = `
    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
    
    .error-notification {
        animation: slideInRight 0.3s ease-out;
    }
`;
document.head.appendChild(errorStyles);
